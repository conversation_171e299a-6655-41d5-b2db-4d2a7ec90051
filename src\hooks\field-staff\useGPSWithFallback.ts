import { useState, useEffect, useCallback, useRef } from 'react';
import { useGPSLocation } from './useGPSLocation';
import { FieldOperationError, <PERSON><PERSON>r<PERSON>and<PERSON> } from '@/utils/errorHandling';
import { toast } from 'sonner';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
  source: 'gps' | 'manual' | 'network';
  address?: string;
  notes?: string;
}

interface GPSWithFallbackState {
  location: LocationData | null;
  error: string | null;
  loading: boolean;
  showManualEntry: boolean;
  gpsRetryCount: number;
  lastGPSAttempt: number | null;
}

export const useGPSWithFallback = () => {
  const {
    location: gpsLocation,
    error: gpsError,
    loading: gpsLoading,
    getCurrentLocation,
    isSupported: gpsSupported,
    movementState,
    batteryOptimized,
    setBatteryOptimized
  } = useGPSLocation();

  const [state, setState] = useState<GPSWithFallbackState>({
    location: null,
    error: null,
    loading: false,
    showManualEntry: false,
    gpsRetryCount: 0,
    lastGPSAttempt: null,
  });

  const manualFallbackListenerRef = useRef<((event: CustomEvent) => void) | null>(null);

  // Convert GPS location to our enhanced format
  useEffect(() => {
    if (gpsLocation) {
      setState(prev => ({
        ...prev,
        location: {
          ...gpsLocation,
          source: 'gps' as const,
        },
        error: null,
        showManualEntry: false,
      }));
    }
  }, [gpsLocation]);

  // Handle GPS errors
  useEffect(() => {
    if (gpsError) {
      setState(prev => ({
        ...prev,
        error: gpsError.message,
        loading: false,
      }));
    }
  }, [gpsError]);

  // Handle GPS loading state
  useEffect(() => {
    setState(prev => ({
      ...prev,
      loading: gpsLoading,
    }));
  }, [gpsLoading]);

  // Listen for manual fallback requests from error handler
  useEffect(() => {
    const handleManualFallbackRequest = (event: CustomEvent) => {
      console.log('Manual fallback requested:', event.detail);
      setState(prev => ({
        ...prev,
        showManualEntry: true,
        loading: false,
      }));
    };

    manualFallbackListenerRef.current = handleManualFallbackRequest;
    window.addEventListener('gps-manual-fallback-requested', handleManualFallbackRequest as EventListener);

    return () => {
      if (manualFallbackListenerRef.current) {
        window.removeEventListener('gps-manual-fallback-requested', manualFallbackListenerRef.current as EventListener);
      }
    };
  }, []);

  // Enhanced location getter with fallback handling
  const getLocationWithFallback = useCallback(async (config?: any): Promise<LocationData> => {
    setState(prev => ({
      ...prev,
      loading: true,
      error: null,
      lastGPSAttempt: Date.now(),
    }));

    try {
      const gpsLocationData = await getCurrentLocation(config);
      
      const enhancedLocation: LocationData = {
        ...gpsLocationData,
        source: 'gps' as const,
      };

      setState(prev => ({
        ...prev,
        location: enhancedLocation,
        loading: false,
        gpsRetryCount: 0,
        showManualEntry: false,
      }));

      return enhancedLocation;

    } catch (error) {
      console.error('GPS location failed:', error);
      
      setState(prev => ({
        ...prev,
        loading: false,
        gpsRetryCount: prev.gpsRetryCount + 1,
        error: error instanceof Error ? error.message : 'GPS location failed',
      }));

      // Handle GPS error with enhanced error handling
      if (error instanceof GeolocationPositionError) {
        const fieldError = FieldOperationError.fromGPSError(error);
        await ErrorHandler.handle(fieldError);
      }

      throw error;
    }
  }, [getCurrentLocation]);

  // Retry GPS location
  const retryGPS = useCallback(async () => {
    setState(prev => ({
      ...prev,
      showManualEntry: false,
      error: null,
    }));

    try {
      return await getLocationWithFallback();
    } catch (error) {
      // Error handling is done in getLocationWithFallback
      throw error;
    }
  }, [getLocationWithFallback]);

  // Handle manual location selection
  const handleManualLocation = useCallback((manualLocation: LocationData) => {
    setState(prev => ({
      ...prev,
      location: manualLocation,
      showManualEntry: false,
      loading: false,
      error: null,
    }));

    toast.success('Manual location set successfully');
  }, []);

  // Cancel manual entry
  const cancelManualEntry = useCallback(() => {
    setState(prev => ({
      ...prev,
      showManualEntry: false,
      loading: false,
    }));
  }, []);

  // Show manual entry dialog
  const showManualEntry = useCallback(() => {
    setState(prev => ({
      ...prev,
      showManualEntry: true,
    }));
  }, []);

  // Check if GPS retry is available (not too many recent attempts)
  const canRetryGPS = useCallback(() => {
    const { gpsRetryCount, lastGPSAttempt } = state;
    const timeSinceLastAttempt = lastGPSAttempt ? Date.now() - lastGPSAttempt : Infinity;
    
    // Allow retry if less than 3 attempts in the last 5 minutes
    return gpsRetryCount < 3 && timeSinceLastAttempt > 30000; // 30 seconds cooldown
  }, [state]);

  return {
    // Location data
    location: state.location,
    error: state.error,
    loading: state.loading,
    
    // GPS-specific data
    gpsSupported,
    movementState,
    batteryOptimized,
    setBatteryOptimized,
    
    // Manual entry state
    showManualEntry: state.showManualEntry,
    gpsRetryCount: state.gpsRetryCount,

    // Actions
    getLocationWithFallback,
    retryGPS,
    handleManualLocation,
    cancelManualEntry,
    canRetryGPS,
  };
};

export type { LocationData };
