import React, { useState, useMemo } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Plus, Target, Users, CheckSquare, Calendar, Search, Filter } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import TaskList from '../TaskList';
import CreateTaskDialog from '../CreateTaskDialog';
import TaskDetailsDialog from './TaskDetailsDialog';
import { 
  useMyTasks, 
  useManagedTasks, 
  useCompletedTasks, 
  useOverdueTasks,
  useCreateTask, 
  useUpdateTaskStatus, 
  TaskFormData 
} from '@/hooks/tasks';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type TaskStatus = Database['public']['Enums']['task_status'];
type TaskPriority = Database['public']['Enums']['task_priority'];

interface TaskFilters {
  search: string;
  status: TaskStatus | 'all';
  priority: TaskPriority | 'all';
  assignee: 'all' | 'me' | 'others';
}

const UnifiedTaskManagement = () => {
  const { profile } = useAuth();
  const { toast } = useToast();
  
  // State management
  const [activeTab, setActiveTab] = useState('my-tasks');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const [filters, setFilters] = useState<TaskFilters>({
    search: '',
    status: 'all',
    priority: 'all',
    assignee: 'all'
  });

  // Role-based permissions
  const isAdmin = profile?.role === 'admin';
  const isProgramOfficer = profile?.role === 'program_officer';
  const isFieldStaff = profile?.role === 'field_staff';
  const canManageTasks = isAdmin || isProgramOfficer;
  const canCreateTasks = canManageTasks || isFieldStaff;

  // Data fetching hooks
  const { data: myTasks = [], isLoading: loadingMyTasks } = useMyTasks();
  const { data: managedTasks = [], isLoading: loadingManagedTasks } = useManagedTasks();
  const { data: completedTasks = [], isLoading: loadingCompletedTasks } = useCompletedTasks();
  const { data: overdueTasks = [], isLoading: loadingOverdueTasks } = useOverdueTasks();
  
  // Mutations
  const createTaskMutation = useCreateTask();
  const updateStatusMutation = useUpdateTaskStatus();

  // Determine available tabs based on role
  const availableTabs = useMemo(() => {
    const tabs = [
      { id: 'my-tasks', label: 'My Tasks', icon: Target, count: myTasks.length }
    ];

    if (canManageTasks) {
      tabs.push(
        { 
          id: 'managed-tasks', 
          label: isAdmin ? 'All Tasks' : 'Managed Tasks', 
          icon: Users, 
          count: managedTasks.length 
        }
      );
    }

    tabs.push(
      { id: 'completed', label: 'Completed', icon: CheckSquare, count: completedTasks.length },
      { id: 'overdue', label: 'Overdue', icon: Calendar, count: overdueTasks.length }
    );

    return tabs;
  }, [myTasks.length, managedTasks.length, completedTasks.length, overdueTasks.length, canManageTasks, isAdmin]);

  // Get current task data based on active tab
  const getCurrentTaskData = () => {
    switch (activeTab) {
      case 'my-tasks':
        return { tasks: myTasks, loading: loadingMyTasks };
      case 'managed-tasks':
        return { tasks: managedTasks, loading: loadingManagedTasks };
      case 'completed':
        return { tasks: completedTasks, loading: loadingCompletedTasks };
      case 'overdue':
        return { tasks: overdueTasks, loading: loadingOverdueTasks };
      default:
        return { tasks: myTasks, loading: loadingMyTasks };
    }
  };

  // Filter tasks based on current filters
  const filteredTasks = useMemo(() => {
    const { tasks } = getCurrentTaskData();
    
    return tasks.filter(task => {
      // Search filter
      if (filters.search && !task.title.toLowerCase().includes(filters.search.toLowerCase()) &&
          !task.description?.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }
      
      // Status filter
      if (filters.status !== 'all' && task.status !== filters.status) {
        return false;
      }
      
      // Priority filter
      if (filters.priority !== 'all' && task.priority !== filters.priority) {
        return false;
      }
      
      // Assignee filter
      if (filters.assignee === 'me' && task.assigned_to !== profile?.id) {
        return false;
      }
      if (filters.assignee === 'others' && task.assigned_to === profile?.id) {
        return false;
      }
      
      return true;
    });
  }, [getCurrentTaskData, filters, profile?.id]);

  // Event handlers
  const handleCreateTask = async (taskData: TaskFormData) => {
    try {
      await createTaskMutation.mutateAsync(taskData);
      toast({
        title: "Success",
        description: "Task created successfully",
      });
      setCreateDialogOpen(false);
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error).message || "Failed to create task",
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = (taskId: string) => {
    setSelectedTaskId(taskId);
  };

  const handleUpdateStatus = (taskId: string, status: TaskStatus) => {
    updateStatusMutation.mutate({ taskId, status });
  };

  const handleFilterChange = (key: keyof TaskFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const clearFilters = () => {
    setFilters({
      search: '',
      status: 'all',
      priority: 'all',
      assignee: 'all'
    });
  };

  // Get page title and description
  const getPageInfo = () => {
    const totalTasks = filteredTasks.length;
    const currentTab = availableTabs.find(tab => tab.id === activeTab);
    
    return {
      title: isFieldStaff ? 'My Tasks' : 'Task Management',
      description: isFieldStaff 
        ? `Your assigned tasks (${totalTasks} total)`
        : `Comprehensive task management (${totalTasks} ${currentTab?.label.toLowerCase() || 'tasks'})`
    };
  };

  const { loading } = getCurrentTaskData();
  const pageInfo = getPageInfo();

  return (
    <PageLayout>
      <PageHeader
        title={pageInfo.title}
        description={pageInfo.description}
        icon={Target}
        actions={canCreateTasks ? [
          {
            label: 'New Task',
            onClick: () => setCreateDialogOpen(true),
            icon: Plus,
          }
        ] : []}
      />

      {/* Field Staff: Simple view without tabs */}
      {isFieldStaff ? (
        <ContentCard noPadding>
          <TaskList
            tasks={filteredTasks}
            loading={loading}
            currentUserId={profile?.id}
            onViewDetails={handleViewDetails}
            onUpdateStatus={handleUpdateStatus}
          />
        </ContentCard>
      ) : (
        /* Admin/Program Officer: Tabbed interface */
        <ContentCard>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            {/* Tab Navigation */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 lg:w-auto">
                {availableTabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                      <Icon className="h-4 w-4" />
                      <span className="hidden sm:inline">{tab.label}</span>
                      <Badge variant="secondary" className="ml-1">
                        {tab.count}
                      </Badge>
                    </TabsTrigger>
                  );
                })}
              </TabsList>

              {/* Filters */}
              <div className="flex flex-wrap items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search tasks..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10 w-48"
                  />
                </div>
                
                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="in_progress">In Progress</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.priority} onValueChange={(value) => handleFilterChange('priority', value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Priority" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Priority</SelectItem>
                    <SelectItem value="low">Low</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="high">High</SelectItem>
                    <SelectItem value="urgent">Urgent</SelectItem>
                  </SelectContent>
                </Select>

                {(filters.search || filters.status !== 'all' || filters.priority !== 'all') && (
                  <Button variant="outline" size="sm" onClick={clearFilters}>
                    <Filter className="h-4 w-4 mr-1" />
                    Clear
                  </Button>
                )}
              </div>
            </div>

            {/* Tab Content */}
            {availableTabs.map((tab) => (
              <TabsContent key={tab.id} value={tab.id} className="mt-0">
                {/* Special alert for overdue tasks */}
                {tab.id === 'overdue' && filteredTasks.length > 0 && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg mb-4">
                    <p className="text-red-700 text-sm">
                      ⚠️ These tasks require immediate attention as they are past their due dates.
                    </p>
                  </div>
                )}
                
                <TaskList
                  tasks={filteredTasks}
                  loading={loading}
                  currentUserId={profile?.id}
                  onViewDetails={handleViewDetails}
                  onUpdateStatus={handleUpdateStatus}
                  showFilters={false} // We handle filters at the parent level
                />
              </TabsContent>
            ))}
          </Tabs>
        </ContentCard>
      )}

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateTask}
        loading={createTaskMutation.isPending}
        canAssignTasks={canManageTasks}
      />

      {/* Task Details Dialog */}
      <TaskDetailsDialog
        taskId={selectedTaskId}
        open={!!selectedTaskId}
        onOpenChange={(open) => {
          if (!open) {
            setSelectedTaskId(null);
          }
        }}
      />
    </PageLayout>
  );
};

export default UnifiedTaskManagement;
