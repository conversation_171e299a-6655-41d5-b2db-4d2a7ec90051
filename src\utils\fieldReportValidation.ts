/**
 * Comprehensive validation utilities for field reports
 * Provides client-side validation with sanitization and security checks
 */

import { Database } from '@/types/database.types';

type FieldActivityType = Database['public']['Enums']['field_activity_type'];

// Photo upload item interface to match OptimizedPhotoUpload
export interface PhotoUploadItem {
  id: string;
  file?: File;
  url?: string;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  progress?: number;
  error?: string;
}

// Facilitator information interface
export interface FacilitatorInfo {
  name: string;
  mobile: string;
  email: string;
}

// Activity feedback interface
export interface ActivityFeedback {
  topic: string;
  what_worked_well: string;
  participant_comments: string;
}

export interface FieldReportFormData {
  activity_type: FieldActivityType;
  round_table_sessions_count: number;
  total_students_attended: number;
  students_per_session: number;
  activities_conducted: string[];
  topics_covered: string[];
  challenges_encountered: string;
  wins_achieved: string;
  general_observations: string;
  lessons_learned: string;
  follow_up_required: boolean;
  follow_up_actions: string;
  photos?: PhotoUploadItem[] | File[] | string[];

  // Enhanced fields from activity report format
  facilitators?: FacilitatorInfo[];
  male_participants?: number;
  female_participants?: number;
  students_primary?: number;
  students_s1?: number;
  students_s2?: number;
  students_s3?: number;
  students_s4?: number;
  students_other?: number;
  champions_count?: number;
  round_tables_primary?: number;
  round_tables_s1?: number;
  round_tables_s2?: number;
  round_tables_s3?: number;
  round_tables_s4?: number;
  round_tables_other?: number;
  activity_feedback?: ActivityFeedback[];
  introduction?: string;
  recommendations?: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  fieldErrors: Record<string, string>;
  warnings: string[];
}

export interface FieldValidationResult {
  isValid: boolean;
  error?: string;
  warning?: string;
}

// Security patterns to detect potentially harmful content
const SECURITY_PATTERNS = [
  /<script[^>]*>.*?<\/script>/gi,
  /javascript:/gi,
  /data:text\/html/gi,
  /vbscript:/gi,
  /onload\s*=/gi,
  /onerror\s*=/gi,
  /onclick\s*=/gi,
  /<iframe[^>]*>/gi,
  /<object[^>]*>/gi,
  /<embed[^>]*>/gi,
];

// Character limits for different field types
const FIELD_LIMITS = {
  challenges_encountered: 2000,
  wins_achieved: 2000,
  lessons_learned: 2000,
  follow_up_actions: 1000,
  activity_item: 200,
  topic_item: 200,
  introduction: 1500,
  recommendations: 2000,
  facilitator_name: 100,
  facilitator_mobile: 20,
  facilitator_email: 100,
  feedback_topic: 200,
  feedback_worked_well: 1000,
  feedback_comments: 1000,
} as const;

/**
 * Sanitizes text input to prevent XSS attacks
 */
export function sanitizeText(text: string): string {
  if (!text) return '';
  
  // Remove potentially harmful patterns
  let sanitized = text;
  SECURITY_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Encode HTML entities
  sanitized = sanitized
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;');
  
  return sanitized.trim();
}

/**
 * Validates text fields with security checks and length limits
 */
export function validateTextField(
  text: string,
  fieldName: string,
  required: boolean = false,
  maxLength: number = 255
): FieldValidationResult {
  // Check if required field is empty
  if (required && (!text || text.trim() === '')) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  // Allow empty optional fields
  if (!text || text.trim() === '') {
    return { isValid: true };
  }
  
  // Check for security threats
  const hasSecurityThreat = SECURITY_PATTERNS.some(pattern => pattern.test(text));
  if (hasSecurityThreat) {
    return { 
      isValid: false, 
      error: `${fieldName} contains potentially harmful content. Please remove any HTML, JavaScript, or script tags.` 
    };
  }
  
  // Check length limits
  if (text.length > maxLength) {
    return { 
      isValid: false, 
      error: `${fieldName} cannot exceed ${maxLength} characters (currently ${text.length})` 
    };
  }
  
  // Warning for very long text
  if (text.length > maxLength * 0.8) {
    return { 
      isValid: true, 
      warning: `${fieldName} is approaching the character limit (${text.length}/${maxLength})` 
    };
  }
  
  return { isValid: true };
}

/**
 * Validates numeric fields with range checks
 */
export function validateNumericField(
  value: number,
  fieldName: string,
  min: number = 0,
  max: number = 10000,
  required: boolean = true
): FieldValidationResult {
  // Check if required field is missing
  if (required && (value === undefined || value === null)) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  // Allow empty optional fields
  if (!required && (value === undefined || value === null)) {
    return { isValid: true };
  }
  
  // Check if it's a valid number
  if (isNaN(value) || !isFinite(value)) {
    return { isValid: false, error: `${fieldName} must be a valid number` };
  }
  
  // Check minimum value
  if (value < min) {
    return { isValid: false, error: `${fieldName} cannot be less than ${min}` };
  }
  
  // Check maximum value
  if (value > max) {
    return { isValid: false, error: `${fieldName} cannot exceed ${max}` };
  }
  
  // Warning for unusual values
  if (fieldName.includes('students') && value > 100) {
    return { 
      isValid: true, 
      warning: `${fieldName} seems unusually high (${value}). Please verify this number.` 
    };
  }
  
  return { isValid: true };
}

/**
 * Validates array fields (activities, topics)
 */
export function validateArrayField(
  items: string[],
  fieldName: string,
  required: boolean = false,
  maxItems: number = 20,
  maxItemLength: number = 200
): FieldValidationResult {
  // Check if required field is empty
  if (required && (!items || items.length === 0)) {
    return { isValid: false, error: `At least one ${fieldName.toLowerCase()} is required` };
  }
  
  // Allow empty optional fields
  if (!items || items.length === 0) {
    return { isValid: true };
  }
  
  // Check maximum number of items
  if (items.length > maxItems) {
    return { 
      isValid: false, 
      error: `Cannot have more than ${maxItems} ${fieldName.toLowerCase()} (currently ${items.length})` 
    };
  }
  
  // Validate each item
  for (let i = 0; i < items.length; i++) {
    const item = items[i];
    
    // Check for empty items
    if (!item || item.trim() === '') {
      return { isValid: false, error: `${fieldName} item ${i + 1} cannot be empty` };
    }
    
    // Validate individual item
    const itemValidation = validateTextField(item, `${fieldName} item ${i + 1}`, true, maxItemLength);
    if (!itemValidation.isValid) {
      return itemValidation;
    }
  }
  
  return { isValid: true };
}

/**
 * Validates facilitator information
 */
export function validateFacilitatorInfo(facilitators: FacilitatorInfo[]): FieldValidationResult {
  if (!facilitators || facilitators.length === 0) {
    return { isValid: true }; // Optional field
  }

  if (facilitators.length > 10) {
    return {
      isValid: false,
      error: 'Cannot have more than 10 facilitators'
    };
  }

  for (let i = 0; i < facilitators.length; i++) {
    const facilitator = facilitators[i];

    // Validate name
    const nameValidation = validateTextField(
      facilitator.name,
      `Facilitator ${i + 1} name`,
      true,
      FIELD_LIMITS.facilitator_name
    );
    if (!nameValidation.isValid) {
      return nameValidation;
    }

    // Validate mobile (basic format check)
    if (!facilitator.mobile || facilitator.mobile.trim() === '') {
      return { isValid: false, error: `Facilitator ${i + 1} mobile number is required` };
    }

    const mobilePattern = /^[\+]?[0-9\s\-\(\)]{7,20}$/;
    if (!mobilePattern.test(facilitator.mobile)) {
      return {
        isValid: false,
        error: `Facilitator ${i + 1} mobile number format is invalid`
      };
    }

    // Validate email (basic format check)
    if (!facilitator.email || facilitator.email.trim() === '') {
      return { isValid: false, error: `Facilitator ${i + 1} email is required` };
    }

    const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailPattern.test(facilitator.email)) {
      return {
        isValid: false,
        error: `Facilitator ${i + 1} email format is invalid`
      };
    }
  }

  return { isValid: true };
}

/**
 * Validates activity feedback information
 */
export function validateActivityFeedback(feedback: ActivityFeedback[]): FieldValidationResult {
  if (!feedback || feedback.length === 0) {
    return { isValid: true }; // Optional field
  }

  if (feedback.length > 20) {
    return {
      isValid: false,
      error: 'Cannot have more than 20 activity feedback entries'
    };
  }

  for (let i = 0; i < feedback.length; i++) {
    const item = feedback[i];

    // Validate topic
    const topicValidation = validateTextField(
      item.topic,
      `Activity feedback ${i + 1} topic`,
      true,
      FIELD_LIMITS.feedback_topic
    );
    if (!topicValidation.isValid) {
      return topicValidation;
    }

    // Validate what worked well
    const workedWellValidation = validateTextField(
      item.what_worked_well,
      `Activity feedback ${i + 1} what worked well`,
      true,
      FIELD_LIMITS.feedback_worked_well
    );
    if (!workedWellValidation.isValid) {
      return workedWellValidation;
    }

    // Validate participant comments
    const commentsValidation = validateTextField(
      item.participant_comments,
      `Activity feedback ${i + 1} participant comments`,
      true,
      FIELD_LIMITS.feedback_comments
    );
    if (!commentsValidation.isValid) {
      return commentsValidation;
    }
  }

  return { isValid: true };
}

/**
 * Education level configuration for flexible student tracking
 */
export const EDUCATION_LEVELS = [
  { key: 'students_primary', label: 'Primary', roundTableKey: 'round_tables_primary' },
  { key: 'students_s1', label: 'Senior 1', roundTableKey: 'round_tables_s1' },
  { key: 'students_s2', label: 'Senior 2', roundTableKey: 'round_tables_s2' },
  { key: 'students_s3', label: 'Senior 3', roundTableKey: 'round_tables_s3' },
  { key: 'students_s4', label: 'Senior 4', roundTableKey: 'round_tables_s4' },
  { key: 'students_other', label: 'Other (A-Level, etc.)', roundTableKey: 'round_tables_other' },
] as const;

/**
 * Helper function to calculate total students across all education levels
 */
export function calculateTotalStudents(data: Partial<FieldReportFormData>): number {
  return EDUCATION_LEVELS.reduce((total, level) => {
    const count = data[level.key as keyof FieldReportFormData] as number || 0;
    return total + count;
  }, 0);
}

/**
 * Helper function to calculate total round tables across all education levels
 */
export function calculateTotalRoundTables(data: Partial<FieldReportFormData>): number {
  return EDUCATION_LEVELS.reduce((total, level) => {
    const count = data[level.roundTableKey as keyof FieldReportFormData] as number || 0;
    return total + count;
  }, 0);
}

/**
 * Helper function to get education level breakdown for display
 */
export function getEducationLevelBreakdown(data: Partial<FieldReportFormData>) {
  return EDUCATION_LEVELS.map(level => ({
    level: level.label,
    students: data[level.key as keyof FieldReportFormData] as number || 0,
    roundTables: data[level.roundTableKey as keyof FieldReportFormData] as number || 0,
  })).filter(item => item.students > 0 || item.roundTables > 0);
}

/**
 * Validates logical relationships between fields
 */
export function validateFieldRelationships(data: FieldReportFormData): FieldValidationResult[] {
  const relationshipErrors: FieldValidationResult[] = [];

  // Calculate totals using helper functions
  const totalStudentsFromLevels = calculateTotalStudents(data);
  const totalRoundTablesFromLevels = calculateTotalRoundTables(data);

  // Students per session cannot exceed total students
  if (data.students_per_session > data.total_students_attended && data.total_students_attended > 0) {
    relationshipErrors.push({
      isValid: false,
      error: 'Students per session cannot exceed total students attended'
    });
  }

  // Check consistency between total_students_attended and education level breakdown
  if (totalStudentsFromLevels > 0 && data.total_students_attended > 0) {
    const difference = Math.abs(totalStudentsFromLevels - data.total_students_attended);
    if (difference > 0) {
      relationshipErrors.push({
        isValid: true,
        warning: `Total students by education level (${totalStudentsFromLevels}) differs from total students attended (${data.total_students_attended}). Please verify these numbers.`
      });
    }
  }

  // Check consistency between round_table_sessions_count and education level breakdown
  if (totalRoundTablesFromLevels > 0 && data.round_table_sessions_count > 0) {
    const difference = Math.abs(totalRoundTablesFromLevels - data.round_table_sessions_count);
    if (difference > 0) {
      relationshipErrors.push({
        isValid: true,
        warning: `Total round tables by education level (${totalRoundTablesFromLevels}) differs from total round table sessions (${data.round_table_sessions_count}). Please verify these numbers.`
      });
    }
  }

  // If follow-up is required, follow-up actions should be provided
  if (data.follow_up_required && (!data.follow_up_actions || data.follow_up_actions.trim() === '')) {
    relationshipErrors.push({
      isValid: false,
      error: 'Follow-up actions are required when follow-up is marked as needed'
    });
  }

  // Round table sessions should be reasonable for student count
  const studentsForRatio = data.total_students_attended || totalStudentsFromLevels;
  const roundTablesForRatio = data.round_table_sessions_count || totalRoundTablesFromLevels;

  if (roundTablesForRatio > 0 && studentsForRatio > 0) {
    const studentsPerRoundTable = studentsForRatio / roundTablesForRatio;
    if (studentsPerRoundTable > 50) {
      relationshipErrors.push({
        isValid: true,
        warning: `Very high student-to-session ratio (${Math.round(studentsPerRoundTable)} students per round table). Please verify these numbers.`
      });
    }
  }

  // Check for gender balance if both male and female participants are provided
  if (data.male_participants !== undefined && data.female_participants !== undefined) {
    const totalByGender = (data.male_participants || 0) + (data.female_participants || 0);
    const totalParticipants = data.total_students_attended || totalStudentsFromLevels;

    // Check if male participants exceeds total
    if (data.male_participants > totalParticipants) {
      relationshipErrors.push({
        isValid: false,
        message: `Male participants (${data.male_participants}) cannot exceed total students present (${totalParticipants})`
      });
    }

    // Since female participants is auto-calculated, we mainly check for consistency
    if (totalByGender > 0 && totalParticipants > 0 && Math.abs(totalByGender - totalParticipants) > 0) {
      relationshipErrors.push({
        isValid: true,
        warning: `Total participants by gender (${totalByGender}) differs from total students (${totalParticipants}). Please verify these numbers.`
      });
    }
  }

  return relationshipErrors;
}

/**
 * Validates individual field in real-time
 */
export function validateField(
  fieldName: keyof FieldReportFormData,
  value: any,
  formData?: Partial<FieldReportFormData>
): FieldValidationResult {
  switch (fieldName) {
    case 'activity_type':
      if (!value) {
        return { isValid: false, error: 'Activity type is required' };
      }
      return { isValid: true };
      
    case 'round_table_sessions_count':
      return validateNumericField(value, 'Round table sessions', 0, 50, false);
      
    case 'total_students_attended':
      return validateNumericField(value, 'Total students attended', 0, 500, false);
      
    case 'students_per_session':
      const result = validateNumericField(value, 'Students per session', 1, 100, false);
      if (result.isValid && formData?.total_students_attended && value > formData.total_students_attended) {
        return { isValid: false, error: 'Students per session cannot exceed total students attended' };
      }
      return result;
      
    case 'activities_conducted':
      return validateArrayField(value, 'Activities conducted', false, 15, FIELD_LIMITS.activity_item);
      
    case 'topics_covered':
      return validateArrayField(value, 'Topics covered', false, 15, FIELD_LIMITS.topic_item);
      
    case 'challenges_encountered':
      return validateTextField(value, 'Challenges encountered', false, FIELD_LIMITS.challenges_encountered);
      
    case 'wins_achieved':
      return validateTextField(value, 'Wins achieved', false, FIELD_LIMITS.wins_achieved);

    case 'lessons_learned':
      return validateTextField(value, 'Lessons learned', false, FIELD_LIMITS.lessons_learned);
      
    case 'follow_up_actions':
      const isRequired = formData?.follow_up_required === true;
      return validateTextField(value, 'Follow-up actions', isRequired, FIELD_LIMITS.follow_up_actions);

    // Enhanced fields validation
    case 'facilitators':
      return validateFacilitatorInfo(value || []);

    case 'male_participants':
      const totalStudents = formData?.total_students_attended || 0;
      if (value > totalStudents) {
        return {
          isValid: false,
          message: `Male participants cannot exceed total students present (${totalStudents})`
        };
      }
      return validateNumericField(value, 'Male participants', 0, totalStudents || 1000, false);

    case 'female_participants':
      // Female participants is auto-calculated, so validation is less strict
      return validateNumericField(value, 'Female participants', 0, 1000, false);

    case 'students_primary':
      return validateNumericField(value, 'Primary students', 0, 500, false);

    case 'students_s1':
      return validateNumericField(value, 'Senior 1 students', 0, 500, false);

    case 'students_s2':
      return validateNumericField(value, 'Senior 2 students', 0, 500, false);

    case 'students_s3':
      return validateNumericField(value, 'Senior 3 students', 0, 500, false);

    case 'students_s4':
      return validateNumericField(value, 'Senior 4 students', 0, 500, false);

    case 'students_other':
      return validateNumericField(value, 'Other level students', 0, 500, false);

    case 'champions_count':
      return validateNumericField(value, 'Champions count', 0, 50, false);

    case 'round_tables_primary':
      return validateNumericField(value, 'Round tables Primary', 0, 20, false);

    case 'round_tables_s1':
      return validateNumericField(value, 'Round tables S1', 0, 20, false);

    case 'round_tables_s2':
      return validateNumericField(value, 'Round tables S2', 0, 20, false);

    case 'round_tables_s3':
      return validateNumericField(value, 'Round tables S3', 0, 20, false);

    case 'round_tables_s4':
      return validateNumericField(value, 'Round tables S4', 0, 20, false);

    case 'round_tables_other':
      return validateNumericField(value, 'Round tables Other', 0, 20, false);

    case 'activity_feedback':
      return validateActivityFeedback(value || []);

    case 'introduction':
      return validateTextField(value, 'Introduction', false, FIELD_LIMITS.introduction);

    case 'recommendations':
      return validateTextField(value, 'Recommendations', false, FIELD_LIMITS.recommendations);

    default:
      return { isValid: true };
  }
}

/**
 * Comprehensive validation for the entire field report form
 */
export function validateFieldReport(data: FieldReportFormData): ValidationResult {
  const errors: string[] = [];
  const fieldErrors: Record<string, string> = {};
  const warnings: string[] = [];

  // Validate required fields
  if (!data.activity_type) {
    errors.push('Activity type is required');
    fieldErrors.activity_type = 'Activity type is required';
  }

  // Validate numeric fields
  const numericFields = [
    { field: 'round_table_sessions_count', min: 0, max: 50, required: false },
    { field: 'total_students_attended', min: 0, max: 500, required: false },
    { field: 'students_per_session', min: 1, max: 100, required: false },
  ] as const;

  numericFields.forEach(({ field, min, max, required }) => {
    const validation = validateNumericField(data[field], field.replace(/_/g, ' '), min, max, required);
    if (!validation.isValid) {
      errors.push(validation.error!);
      fieldErrors[field] = validation.error!;
    } else if (validation.warning) {
      warnings.push(validation.warning);
    }
  });

  // Validate text fields
  const textFields = [
    { field: 'challenges_encountered', limit: FIELD_LIMITS.challenges_encountered, required: false },
    { field: 'wins_achieved', limit: FIELD_LIMITS.wins_achieved, required: false },
    { field: 'lessons_learned', limit: FIELD_LIMITS.lessons_learned, required: false },
    { field: 'follow_up_actions', limit: FIELD_LIMITS.follow_up_actions, required: data.follow_up_required },
  ] as const;

  textFields.forEach(({ field, limit, required }) => {
    const validation = validateTextField(data[field], field.replace(/_/g, ' '), required, limit);
    if (!validation.isValid) {
      errors.push(validation.error!);
      fieldErrors[field] = validation.error!;
    } else if (validation.warning) {
      warnings.push(validation.warning);
    }
  });

  // Validate array fields
  const arrayFields = [
    { field: 'activities_conducted', maxItems: 15, maxItemLength: FIELD_LIMITS.activity_item, required: false },
    { field: 'topics_covered', maxItems: 15, maxItemLength: FIELD_LIMITS.topic_item, required: false },
  ] as const;

  arrayFields.forEach(({ field, maxItems, maxItemLength, required }) => {
    const validation = validateArrayField(data[field], field.replace(/_/g, ' '), required, maxItems, maxItemLength);
    if (!validation.isValid) {
      errors.push(validation.error!);
      fieldErrors[field] = validation.error!;
    } else if (validation.warning) {
      warnings.push(validation.warning);
    }
  });

  // Validate enhanced fields
  if (data.facilitators) {
    const facilitatorValidation = validateFacilitatorInfo(data.facilitators);
    if (!facilitatorValidation.isValid) {
      errors.push(facilitatorValidation.error!);
      fieldErrors.facilitators = facilitatorValidation.error!;
    } else if (facilitatorValidation.warning) {
      warnings.push(facilitatorValidation.warning);
    }
  }

  if (data.activity_feedback) {
    const feedbackValidation = validateActivityFeedback(data.activity_feedback);
    if (!feedbackValidation.isValid) {
      errors.push(feedbackValidation.error!);
      fieldErrors.activity_feedback = feedbackValidation.error!;
    } else if (feedbackValidation.warning) {
      warnings.push(feedbackValidation.warning);
    }
  }

  // Validate enhanced text fields
  const enhancedTextFields = [
    { field: 'introduction', limit: FIELD_LIMITS.introduction, required: false },
    { field: 'recommendations', limit: FIELD_LIMITS.recommendations, required: false },
    { field: 'way_forward', limit: FIELD_LIMITS.way_forward, required: false },
  ] as const;

  enhancedTextFields.forEach(({ field, limit, required }) => {
    if (data[field]) {
      const validation = validateTextField(data[field], field.replace(/_/g, ' '), required, limit);
      if (!validation.isValid) {
        errors.push(validation.error!);
        fieldErrors[field] = validation.error!;
      } else if (validation.warning) {
        warnings.push(validation.warning);
      }
    }
  });

  // Validate enhanced numeric fields
  const enhancedNumericFields = [
    { field: 'male_participants', min: 0, max: 1000, required: false },
    { field: 'female_participants', min: 0, max: 1000, required: false },
    { field: 'students_primary', min: 0, max: 500, required: false },
    { field: 'students_s1', min: 0, max: 500, required: false },
    { field: 'students_s2', min: 0, max: 500, required: false },
    { field: 'students_s3', min: 0, max: 500, required: false },
    { field: 'students_s4', min: 0, max: 500, required: false },
    { field: 'students_other', min: 0, max: 500, required: false },
    { field: 'champions_count', min: 0, max: 50, required: false },
    { field: 'round_tables_primary', min: 0, max: 20, required: false },
    { field: 'round_tables_s1', min: 0, max: 20, required: false },
    { field: 'round_tables_s2', min: 0, max: 20, required: false },
    { field: 'round_tables_s3', min: 0, max: 20, required: false },
    { field: 'round_tables_s4', min: 0, max: 20, required: false },
    { field: 'round_tables_other', min: 0, max: 20, required: false },
  ] as const;

  enhancedNumericFields.forEach(({ field, min, max, required }) => {
    if (data[field] !== undefined && data[field] !== null) {
      const validation = validateNumericField(data[field], field.replace(/_/g, ' '), min, max, required);
      if (!validation.isValid) {
        errors.push(validation.error!);
        fieldErrors[field] = validation.error!;
      } else if (validation.warning) {
        warnings.push(validation.warning);
      }
    }
  });

  // Validate field relationships
  const relationshipValidations = validateFieldRelationships(data);
  relationshipValidations.forEach(validation => {
    if (!validation.isValid) {
      errors.push(validation.error!);
    } else if (validation.warning) {
      warnings.push(validation.warning);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    fieldErrors,
    warnings,
  };
}

/**
 * Validates photo uploads
 */
export function validatePhotoUpload(file: File): FieldValidationResult {
  const maxSizeKB = 5000; // 5MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

  // Check file type
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: 'Only JPEG, PNG, and WebP images are allowed'
    };
  }

  // Check file size
  const fileSizeKB = file.size / 1024;
  if (fileSizeKB > maxSizeKB) {
    return {
      isValid: false,
      error: `Image size cannot exceed ${maxSizeKB / 1024}MB (current: ${(fileSizeKB / 1024).toFixed(1)}MB)`
    };
  }

  // Warning for large files
  if (fileSizeKB > maxSizeKB * 0.7) {
    return {
      isValid: true,
      warning: `Large image file (${(fileSizeKB / 1024).toFixed(1)}MB). Consider compressing for faster upload.`
    };
  }

  return { isValid: true };
}

/**
 * Sanitizes the entire form data before submission
 */
export function sanitizeFieldReportData(data: FieldReportFormData): FieldReportFormData {
  return {
    ...data,
    activities_conducted: data.activities_conducted.map(sanitizeText),
    topics_covered: data.topics_covered.map(sanitizeText),
    challenges_encountered: sanitizeText(data.challenges_encountered),
    wins_achieved: sanitizeText(data.wins_achieved),
    general_observations: sanitizeText(data.general_observations),
    lessons_learned: sanitizeText(data.lessons_learned),
    follow_up_actions: sanitizeText(data.follow_up_actions),

    // Sanitize enhanced fields
    introduction: data.introduction ? sanitizeText(data.introduction) : undefined,
    recommendations: data.recommendations ? sanitizeText(data.recommendations) : undefined,

    // Sanitize facilitator information
    facilitators: data.facilitators?.map(facilitator => ({
      name: sanitizeText(facilitator.name),
      mobile: sanitizeText(facilitator.mobile),
      email: sanitizeText(facilitator.email),
    })),

    // Sanitize activity feedback
    activity_feedback: data.activity_feedback?.map(feedback => ({
      topic: sanitizeText(feedback.topic),
      what_worked_well: sanitizeText(feedback.what_worked_well),
      participant_comments: sanitizeText(feedback.participant_comments),
    })),
  };
}
