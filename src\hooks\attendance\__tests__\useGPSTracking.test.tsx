import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useGPSPosition, useGPSCheckIn, useGPSCheckOut } from '../useGPSTracking';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/integrations/supabase/client');
jest.mock('sonner');
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    profile: { id: 'test-user-id', role: 'field_staff' }
  })
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock geolocation
const mockGeolocation = {
  getCurrentPosition: jest.fn(),
  watchPosition: jest.fn(),
  clearWatch: jest.fn(),
};

Object.defineProperty(global.navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true,
});

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useGPSPosition', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Object.defineProperty(global.navigator, 'onLine', {
      value: true,
      writable: true,
    });
  });

  it('should get current GPS position successfully', async () => {
    const mockPosition = {
      coords: {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 10,
        speed: null,
        heading: null,
      },
      timestamp: Date.now(),
    };

    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success(mockPosition);
    });

    const { result } = renderHook(() => useGPSPosition(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.getCurrentPosition();
    });

    expect(result.current.position).toEqual({
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 10,
      timestamp: mockPosition.timestamp,
      speed: undefined,
      heading: undefined,
    });
    expect(result.current.error).toBeNull();
  });

  it('should handle GPS permission denied error', async () => {
    const mockError = {
      code: 1, // PERMISSION_DENIED
      message: 'Permission denied',
      PERMISSION_DENIED: 1,
      POSITION_UNAVAILABLE: 2,
      TIMEOUT: 3,
    };

    mockGeolocation.getCurrentPosition.mockImplementation((_, error) => {
      error(mockError);
    });

    const { result } = renderHook(() => useGPSPosition(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      try {
        await result.current.getCurrentPosition();
      } catch (error) {
        // Expected to throw
      }
    });

    expect(result.current.error).toBe('Location access denied. Please enable location permissions in your browser settings.');
  });

  it('should retry GPS on timeout with exponential backoff', async () => {
    const mockError = {
      code: 3, // TIMEOUT
      message: 'Timeout',
      PERMISSION_DENIED: 1,
      POSITION_UNAVAILABLE: 2,
      TIMEOUT: 3,
    };

    let callCount = 0;
    mockGeolocation.getCurrentPosition.mockImplementation((_, error) => {
      callCount++;
      error(mockError);
    });

    const { result } = renderHook(() => useGPSPosition(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.startWatching();
    });

    // Wait for initial call and first retry
    await waitFor(() => {
      expect(callCount).toBeGreaterThan(1);
    }, { timeout: 10000 });

    expect(result.current.error).toContain('GPS signal is taking too long');
  });
});

describe('useGPSCheckIn', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
  });

  it('should perform GPS check-in successfully', async () => {
    const mockLocationData = {
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 15,
      address: '123 Test St, New York, NY',
    };

    const { result } = renderHook(() => useGPSCheckIn(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.mutateAsync(mockLocationData);
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('gps_checkin', {
      p_latitude: 40.7128,
      p_longitude: -74.0060,
      p_accuracy: 15,
      p_address: '123 Test St, New York, NY',
      p_verification_method: 'gps',
    });

    expect(mockToast.success).toHaveBeenCalledWith('GPS check-in successful!');
  });

  it('should handle check-in errors with specific messages', async () => {
    const mockError = new Error('Network error');
    mockSupabase.rpc.mockRejectedValue(mockError);

    const { result } = renderHook(() => useGPSCheckIn(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      try {
        await result.current.mutateAsync({
          latitude: 40.7128,
          longitude: -74.0060,
          accuracy: 15,
        });
      } catch (error) {
        // Expected to throw
      }
    });

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Network Error',
        description: expect.stringContaining('Check your internet connection'),
        variant: 'destructive',
      })
    );
  });

  it('should warn about poor GPS accuracy', async () => {
    const mockLocationData = {
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 150, // Poor accuracy
    };

    const { result } = renderHook(() => useGPSCheckIn(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.mutateAsync(mockLocationData);
    });

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Poor GPS Signal',
        description: expect.stringContaining('GPS accuracy is too low'),
        variant: 'destructive',
      })
    );
  });
});

describe('useGPSCheckOut', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    type MockQueryBuilder = {
      select: jest.Mock;
      eq: jest.Mock;
      order: jest.Mock;
      limit: jest.Mock;
      maybeSingle: jest.Mock;
      update: jest.Mock;
    };

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({
        data: { id: 'test-log-id', check_in_status: 'checked_in', check_in_time: new Date().toISOString() },
        error: null,
      }),
      update: jest.fn().mockReturnThis(),
    } as MockQueryBuilder);
  });

  it('should perform GPS check-out successfully', async () => {
    const mockUpdateResponse = {
      data: {
        id: 'test-log-id',
        check_in_status: 'checked_out',
        check_in_time: new Date().toISOString(),
        check_out_time: new Date().toISOString(),
      },
      error: null,
    };

    (mockSupabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn()
        .mockResolvedValueOnce({
          data: { id: 'test-log-id' },
          error: null,
        })
        .mockResolvedValueOnce({
          data: { id: 'test-log-id', check_in_status: 'checked_in', check_in_time: new Date().toISOString() },
          error: null,
        }),
      update: jest.fn().mockReturnThis(),
    });

    (mockSupabase.from().update().eq().select().maybeSingle as jest.Mock).mockResolvedValue(mockUpdateResponse);

    const { result } = renderHook(() => useGPSCheckOut(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.mutateAsync();
    });

    expect(mockToast.success).toHaveBeenCalledWith('GPS check-out successful!');
  });

  it('should handle no active check-in session', async () => {
    (mockSupabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
    });

    const { result } = renderHook(() => useGPSCheckOut(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      try {
        await result.current.mutateAsync();
      } catch (error) {
        // Expected to throw
      }
    });

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'No Active Session',
        description: 'No active check-in session found. You may already be checked out.',
        variant: 'destructive',
      })
    );
  });

  it('should handle already checked out session', async () => {
    (mockSupabase.from as jest.Mock).mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn()
        .mockResolvedValueOnce({
          data: { id: 'test-log-id' },
          error: null,
        })
        .mockResolvedValueOnce({
          data: { id: 'test-log-id', check_in_status: 'checked_out' },
          error: null,
        }),
    });

    const { result } = renderHook(() => useGPSCheckOut(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      try {
        await result.current.mutateAsync();
      } catch (error) {
        // Expected to throw
      }
    });

    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Already Checked Out',
        description: 'This session has already been checked out.',
        variant: 'destructive',
      })
    );
  });
});
