import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { storageManager } from '@/utils/storageManager';

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => Object.keys(store)[index] || null,
  };
})();

// Mock IndexedDB
const mockIndexedDB = {
  open: vi.fn().mockImplementation(() => ({
    result: {
      transaction: vi.fn().mockReturnValue({
        objectStore: vi.fn().mockReturnValue({
          getAll: vi.fn().mockImplementation(() => ({
            onsuccess: null,
            onerror: null,
          })),
          delete: vi.fn().mockImplementation(() => ({
            onsuccess: null,
            onerror: null,
          })),
        }),
      }),
    },
    onsuccess: null,
    onerror: null,
    onupgradeneeded: null,
  })),
};

// Mock navigator.storage
const mockNavigatorStorage = {
  estimate: vi.fn().mockResolvedValue({
    usage: 1024 * 1024, // 1MB
    quota: 50 * 1024 * 1024, // 50MB
  }),
};

describe('StorageManager', () => {
  beforeEach(() => {
    // Setup mocks
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    Object.defineProperty(window, 'indexedDB', {
      value: mockIndexedDB,
      writable: true,
    });

    Object.defineProperty(navigator, 'storage', {
      value: mockNavigatorStorage,
      writable: true,
    });

    // Clear localStorage
    localStorageMock.clear();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('Storage Usage Calculation', () => {
    it('should calculate localStorage usage correctly', async () => {
      // Add some test data
      localStorageMock.setItem('test_key_1', 'test_value_1');
      localStorageMock.setItem('test_key_2', 'test_value_2');

      const usage = await storageManager.getStorageUsage();

      expect(usage.localStorage).toBeGreaterThan(0);
      expect(usage.total).toBeGreaterThan(0);
      expect(usage.percentage).toBeGreaterThan(0);
    });

    it('should handle storage estimation errors gracefully', async () => {
      mockNavigatorStorage.estimate.mockRejectedValueOnce(new Error('Storage API not available'));

      const usage = await storageManager.getStorageUsage();

      expect(usage.indexedDB).toBe(0);
      expect(usage.localStorage).toBeGreaterThanOrEqual(0);
    });
  });

  describe('Cleanup Functionality', () => {
    beforeEach(() => {
      // Setup test data
      const now = Date.now();
      const oldTimestamp = now - (8 * 24 * 60 * 60 * 1000); // 8 days ago
      const recentTimestamp = now - (1 * 60 * 60 * 1000); // 1 hour ago

      // Add offline data with mixed ages
      const offlineData = [
        {
          id: 'old_item_1',
          type: 'check_in',
          data: { test: 'data' },
          timestamp: oldTimestamp,
          retryCount: 0,
          maxRetries: 5,
          priority: 'MEDIUM',
        },
        {
          id: 'recent_item_1',
          type: 'check_in',
          data: { test: 'data' },
          timestamp: recentTimestamp,
          retryCount: 0,
          maxRetries: 5,
          priority: 'HIGH',
        },
        {
          id: 'failed_item_1',
          type: 'check_in',
          data: { test: 'data' },
          timestamp: oldTimestamp,
          retryCount: 5,
          maxRetries: 5,
          priority: 'LOW',
        },
      ];

      localStorageMock.setItem('field_staff_offline_data', JSON.stringify(offlineData));

      // Add old conflicts
      const conflicts = [
        {
          id: 'old_conflict_1',
          localData: { test: 'local' },
          serverData: { test: 'server' },
          conflictFields: ['test'],
          timestamp: oldTimestamp,
        },
        {
          id: 'recent_conflict_1',
          localData: { test: 'local' },
          serverData: { test: 'server' },
          conflictFields: ['test'],
          timestamp: recentTimestamp,
        },
      ];

      localStorageMock.setItem('field_staff_sync_conflicts', JSON.stringify(conflicts));
    });

    it('should remove old successful items during cleanup', async () => {
      const report = await storageManager.performCleanup(true);

      expect(report.itemsRemoved).toBeGreaterThan(0);
      expect(report.sizeFreed).toBeGreaterThan(0);

      const remainingData = JSON.parse(
        localStorageMock.getItem('field_staff_offline_data') || '[]'
      );

      // Should keep recent items and failed items (they need manual intervention)
      expect(remainingData.length).toBeLessThan(3);
      expect(remainingData.some((item: any) => item.id === 'recent_item_1')).toBe(true);
    });

    it('should remove old conflicts during cleanup', async () => {
      const report = await storageManager.performCleanup(true);

      const remainingConflicts = JSON.parse(
        localStorageMock.getItem('field_staff_sync_conflicts') || '[]'
      );

      // Should keep recent conflicts but remove old ones
      expect(remainingConflicts.length).toBeLessThan(2);
      expect(remainingConflicts.some((conflict: any) => conflict.id === 'recent_conflict_1')).toBe(true);
    });

    it('should respect item limits during cleanup', async () => {
      // Create many items to test limit enforcement
      const manyItems = Array.from({ length: 600 }, (_, i) => ({
        id: `item_${i}`,
        type: 'check_in',
        data: { test: 'data' },
        timestamp: Date.now() - (i * 1000), // Spread timestamps
        retryCount: 0,
        maxRetries: 5,
        priority: i % 2 === 0 ? 'HIGH' : 'LOW',
      }));

      localStorageMock.setItem('field_staff_offline_data', JSON.stringify(manyItems));

      const report = await storageManager.performCleanup(true);

      const remainingData = JSON.parse(
        localStorageMock.getItem('field_staff_offline_data') || '[]'
      );

      // Should enforce the 500 item limit
      expect(remainingData.length).toBeLessThanOrEqual(500);
      expect(report.itemsRemoved).toBeGreaterThan(0);
    });
  });

  describe('Storage Health Assessment', () => {
    it('should return healthy status for low usage', async () => {
      // Add minimal data
      localStorageMock.setItem('small_data', 'test');

      const health = await storageManager.getStorageHealth();

      expect(health.status).toBe('healthy');
      expect(health.recommendations).toHaveLength(0);
    });

    it('should return warning status for medium usage', async () => {
      // Mock high usage
      mockNavigatorStorage.estimate.mockResolvedValueOnce({
        usage: 45 * 1024 * 1024, // 45MB (75% of 60MB total)
        quota: 50 * 1024 * 1024,
      });

      const health = await storageManager.getStorageHealth();

      expect(health.status).toBe('warning');
      expect(health.recommendations.length).toBeGreaterThan(0);
    });

    it('should return critical status for very high usage', async () => {
      // Mock critical usage
      mockNavigatorStorage.estimate.mockResolvedValueOnce({
        usage: 55 * 1024 * 1024, // 55MB (>90% of 60MB total)
        quota: 50 * 1024 * 1024,
      });

      const health = await storageManager.getStorageHealth();

      expect(health.status).toBe('critical');
      expect(health.recommendations.length).toBeGreaterThan(0);
      expect(health.recommendations.some(rec => rec.includes('critically full'))).toBe(true);
    });
  });

  describe('Cleanup History', () => {
    it('should maintain cleanup history', async () => {
      const report = await storageManager.performCleanup(true);

      const history = storageManager.getCleanupHistory();

      expect(history).toHaveLength(1);
      expect(history[0].timestamp).toBe(report.timestamp);
      expect(history[0].itemsRemoved).toBe(report.itemsRemoved);
    });

    it('should limit cleanup history to 10 entries', async () => {
      // Perform multiple cleanups
      for (let i = 0; i < 12; i++) {
        await storageManager.performCleanup(true);
        // Add small delay to ensure different timestamps
        await new Promise(resolve => setTimeout(resolve, 1));
      }

      const history = storageManager.getCleanupHistory();

      expect(history.length).toBeLessThanOrEqual(10);
    });
  });

  describe('Error Handling', () => {
    it('should handle localStorage errors gracefully', async () => {
      // Mock localStorage error
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = vi.fn().mockImplementation(() => {
        throw new Error('Storage quota exceeded');
      });

      const report = await storageManager.performCleanup(true);

      expect(report.errors.length).toBeGreaterThan(0);
      expect(report.errors.some(error => error.includes('cleanup failed'))).toBe(true);

      // Restore original method
      localStorageMock.setItem = originalSetItem;
    });

    it('should handle missing data gracefully', async () => {
      // Clear all data
      localStorageMock.clear();

      const usage = await storageManager.getStorageUsage();
      const health = await storageManager.getStorageHealth();

      expect(usage.total).toBe(0);
      expect(health.status).toBe('healthy');
    });
  });

  describe('Automatic Cleanup Triggers', () => {
    it('should trigger cleanup when storage threshold is exceeded', async () => {
      // Mock high usage to trigger automatic cleanup
      mockNavigatorStorage.estimate.mockResolvedValue({
        usage: 50 * 1024 * 1024, // 50MB (>80% of 60MB total)
        quota: 50 * 1024 * 1024,
      });

      const shouldClean = await storageManager.shouldCleanup();

      expect(shouldClean).toBe(true);
    });

    it('should not trigger cleanup when storage is below threshold', async () => {
      // Mock low usage
      mockNavigatorStorage.estimate.mockResolvedValue({
        usage: 10 * 1024 * 1024, // 10MB (<80% of 60MB total)
        quota: 50 * 1024 * 1024,
      });

      const shouldClean = await storageManager.shouldCleanup();

      expect(shouldClean).toBe(false);
    });
  });
});
