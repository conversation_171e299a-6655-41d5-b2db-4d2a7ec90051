/**
 * Refactored useOfflineSync hook - simplified and modular
 * Orchestrates the extracted utility modules for offline sync functionality
 */

import { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';

// Import types
import { 
  OfflineData, 
  OfflineSyncStatus, 
  SyncConflict, 
  ConflictData,
  SyncStats,
  StorageStats,
  CleanupResult,
  SyncResult,
  BatchSyncOptions,
  DEFAULT_CONFIG,
  CONFLICT_RESOLUTION
} from '@/types/offlineSync.types';

// Import utilities
import { 
  loadOfflineData, 
  saveOfflineData,
  loadConflicts,
  saveConflicts,
  addToOfflineQueue as addToQueue,
  removeFromOfflineQueue as removeFromQueue,
  clearOfflineData as clearStorage,
  calculateStorageStats
} from '@/utils/offlineStorage';

import { 
  syncAllOfflineData,
  retryFailedItems,
  checkOnlineStatus,
  testServerConnectivity
} from '@/utils/syncOperations';

import { 
  getAllConflicts,
  removeConflict,
  resolveConflict as resolveConflictUtil
} from '@/utils/conflictResolution';

import { 
  performComprehensiveCleanup,
  isCleanupNeeded,
  getCleanupRecommendations
} from '@/utils/storageCleanup';

export const useOfflineSync = () => {
  const queryClient = useQueryClient();
  
  // State management
  const [syncStatus, setSyncStatus] = useState<OfflineSyncStatus>({
    isOnline: checkOnlineStatus(),
    pendingItems: 0,
    lastSyncTime: null,
    isSyncing: false,
    syncProgress: 0,
    failedItems: 0,
    conflictItems: 0,
    storageUsagePercent: 0,
    lastCleanup: null,
    totalStorageSize: 0,
  });

  const [conflicts, setConflicts] = useState<ConflictData[]>([]);

  // Update sync status
  const updateSyncStatus = useCallback(async () => {
    try {
      const offlineData = loadOfflineData();
      const conflictData = getAllConflicts();
      const stats = await calculateStorageStats();
      
      setSyncStatus(prev => ({
        ...prev,
        pendingItems: offlineData.length,
        failedItems: offlineData.filter(item => item.retryCount >= item.maxRetries).length,
        conflictItems: conflictData.length,
        storageUsagePercent: stats.storageUsagePercent,
        totalStorageSize: stats.totalSize,
        lastCleanup: stats.lastCleanup ? new Date(stats.lastCleanup) : null,
      }));
      
      setConflicts(conflictData);
    } catch (error) {
      console.error('Failed to update sync status:', error);
    }
  }, []);

  // Add item to offline queue
  const addToOfflineQueue = useCallback((
    type: OfflineData['type'],
    data: Record<string, unknown>,
    priority: OfflineData['priority'] = 'MEDIUM'
  ): string => {
    try {
      const id = addToQueue(type, data, priority);
      updateSyncStatus();
      toast.success('Data saved offline');
      return id;
    } catch (error) {
      console.error('Failed to add to offline queue:', error);
      toast.error('Failed to save data offline');
      throw error;
    }
  }, [updateSyncStatus]);

  // Remove item from offline queue
  const removeFromOfflineQueue = useCallback((id: string): boolean => {
    try {
      const success = removeFromQueue(id);
      if (success) {
        updateSyncStatus();
      }
      return success;
    } catch (error) {
      console.error('Failed to remove from offline queue:', error);
      return false;
    }
  }, [updateSyncStatus]);

  // Sync offline data
  const syncOfflineData = useCallback(async (options?: BatchSyncOptions): Promise<SyncResult> => {
    if (!checkOnlineStatus()) {
      toast.error('No internet connection');
      return { success: false, processed: 0, failed: 0, conflicts: 0, errors: ['No internet connection'] };
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, syncProgress: 0 }));

    try {
      const result = await syncAllOfflineData(options);
      
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        syncProgress: 100,
        lastSyncTime: new Date(),
      }));

      await updateSyncStatus();

      if (result.success) {
        toast.success(`Synced ${result.processed} items successfully`);
        queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
        queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      } else {
        toast.error(`Sync completed with ${result.failed} failures and ${result.conflicts} conflicts`);
      }

      return result;
    } catch (error) {
      setSyncStatus(prev => ({ ...prev, isSyncing: false, syncProgress: 0 }));
      console.error('Sync failed:', error);
      toast.error('Sync operation failed');
      throw error;
    }
  }, [queryClient, updateSyncStatus, checkOnlineStatus, syncAllOfflineData]);

  // Clear offline data
  const clearOfflineData = useCallback(() => {
    try {
      clearStorage();
      updateSyncStatus();
      toast.success('Offline data cleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      toast.error('Failed to clear offline data');
    }
  }, [updateSyncStatus, clearStorage]);

  // Resolve conflict
  const resolveConflict = useCallback((
    conflictId: string,
    strategy: keyof typeof CONFLICT_RESOLUTION,
    customResolution?: Record<string, unknown>
  ): boolean => {
    try {
      const conflict = conflicts.find(c => c.id === conflictId);
      if (!conflict) {
        toast.error('Conflict not found');
        return false;
      }

      const resolvedData = resolveConflictUtil(conflict, strategy, customResolution);
      const success = removeConflict(conflictId);
      
      if (success) {
        updateSyncStatus();
        toast.success('Conflict resolved');
        
        // Add resolved data back to sync queue if needed
        if (strategy !== 'SERVER_WINS') {
          addToOfflineQueue(conflict.type as OfflineData['type'], resolvedData, 'HIGH');
        }
      }
      
      return success;
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      toast.error('Failed to resolve conflict');
      return false;
    }
  }, [conflicts, updateSyncStatus, addToOfflineQueue, resolveConflictUtil, removeConflict]);

  // Get sync statistics
  const getSyncStats = useCallback((): SyncStats => {
    const offlineData = loadOfflineData();
    const conflictData = getAllConflicts();

    return {
      totalPending: offlineData.length,
      highPriority: offlineData.filter(item => item.priority === 'HIGH' || item.priority === 'CRITICAL').length,
      failed: offlineData.filter(item => item.retryCount >= item.maxRetries).length,
      conflicts: conflictData.length,
      oldestItem: offlineData.length > 0 ? Math.min(...offlineData.map(item => item.timestamp)) : null,
    };
  }, [loadOfflineData, getAllConflicts]);

  // Get storage statistics
  const getStorageStats = useCallback(async (): Promise<StorageStats> => {
    return calculateStorageStats();
  }, [calculateStorageStats]);

  // Perform cleanup
  const performCleanup = useCallback(async (forceCleanup = false): Promise<CleanupResult> => {
    try {
      const result = await performComprehensiveCleanup(forceCleanup);
      await updateSyncStatus();
      
      if (result.itemsRemoved > 0 || result.conflictsRemoved > 0) {
        toast.success(`Cleanup completed: ${result.itemsRemoved} items and ${result.conflictsRemoved} conflicts removed`);
      }
      
      return result;
    } catch (error) {
      console.error('Cleanup failed:', error);
      toast.error('Cleanup operation failed');
      throw error;
    }
  }, [updateSyncStatus, performComprehensiveCleanup]);

  // Monitor online status
  useEffect(() => {
    const handleOnlineStatusChange = () => {
      const isOnline = checkOnlineStatus();
      setSyncStatus(prev => ({ ...prev, isOnline }));
      
      if (isOnline) {
        toast.success('Connection restored');
        // Auto-sync when coming back online
        syncOfflineData({ retryFailedItems: true });
      } else {
        toast.warning('Connection lost - working offline');
      }
    };

    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);

    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, [syncOfflineData, checkOnlineStatus]);

  // Initialize and periodic updates
  useEffect(() => {
    updateSyncStatus();
    
    // Update status every 30 seconds
    const interval = setInterval(updateSyncStatus, 30000);
    
    return () => clearInterval(interval);
  }, [updateSyncStatus]);

  // Auto-cleanup check
  useEffect(() => {
    const checkAndCleanup = async () => {
      try {
        const needsCleanup = await isCleanupNeeded();
        if (needsCleanup) {
          await performCleanup();
        }
      } catch (error) {
        console.error('Auto-cleanup check failed:', error);
      }
    };

    // Check for cleanup every hour
    const cleanupInterval = setInterval(checkAndCleanup, 60 * 60 * 1000);
    
    return () => clearInterval(cleanupInterval);
  }, [performCleanup, isCleanupNeeded]);

  return {
    syncStatus,
    conflicts,
    addToOfflineQueue,
    removeFromOfflineQueue,
    syncOfflineData,
    clearOfflineData,
    loadOfflineData,
    resolveConflict,
    getSyncStats,
    getStorageStats,
    performComprehensiveCleanup: performCleanup,
  };
};
