import { useState, useEffect, useCallback } from 'react';
import { storageManager, StorageUsage, CleanupReport } from '@/utils/storageManager';
import { toast } from 'sonner';

export interface StorageMonitorState {
  usage: StorageUsage | null;
  health: {
    status: 'healthy' | 'warning' | 'critical';
    usage: StorageUsage;
    recommendations: string[];
  } | null;
  cleanupHistory: CleanupReport[];
  isLoading: boolean;
  lastUpdated: Date | null;
}

export interface StorageMonitorActions {
  refresh: () => Promise<void>;
  performCleanup: () => Promise<CleanupReport | null>;
  getStorageBreakdown: () => Promise<{
    offlineData: number;
    photos: number;
    conflicts: number;
    other: number;
  }>;
}

export const useStorageMonitor = (options: {
  autoRefresh?: boolean;
  refreshInterval?: number;
  alertThresholds?: {
    warning: number;
    critical: number;
  };
} = {}) => {
  const {
    autoRefresh = true,
    refreshInterval = 60000, // 1 minute
    alertThresholds = { warning: 80, critical: 90 }
  } = options;

  const [state, setState] = useState<StorageMonitorState>({
    usage: null,
    health: null,
    cleanupHistory: [],
    isLoading: true,
    lastUpdated: null,
  });

  const [isCleaningUp, setIsCleaningUp] = useState(false);
  const [alertsShown, setAlertsShown] = useState<Set<string>>(new Set());

  const refresh = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true }));

      const [usage, health, history] = await Promise.all([
        storageManager.getStorageUsage(),
        storageManager.getStorageHealth(),
        Promise.resolve(storageManager.getCleanupHistory()),
      ]);

      setState({
        usage,
        health,
        cleanupHistory: history,
        isLoading: false,
        lastUpdated: new Date(),
      });

      // Show alerts based on thresholds
      if (usage.percentage >= alertThresholds.critical) {
        const alertKey = `critical-${Math.floor(usage.percentage)}`;
        if (!alertsShown.has(alertKey)) {
          toast.error(
            `Storage critically full (${usage.percentage.toFixed(1)}%)! Immediate cleanup required.`,
            {
              duration: 10000,
              action: {
                label: 'Clean Up',
                onClick: () => performCleanup(),
              },
            }
          );
          setAlertsShown(prev => new Set(prev).add(alertKey));
        }
      } else if (usage.percentage >= alertThresholds.warning) {
        const alertKey = `warning-${Math.floor(usage.percentage / 5) * 5}`; // Group by 5% intervals
        if (!alertsShown.has(alertKey)) {
          toast.warning(
            `Storage usage high (${usage.percentage.toFixed(1)}%). Consider cleaning up offline data.`,
            {
              duration: 5000,
              action: {
                label: 'Clean Up',
                onClick: () => performCleanup(),
              },
            }
          );
          setAlertsShown(prev => new Set(prev).add(alertKey));
        }
      }

    } catch (error) {
      console.error('Error refreshing storage data:', error);
      setState(prev => ({ ...prev, isLoading: false }));
    }
  }, [alertThresholds, alertsShown]);

  const performCleanup = useCallback(async (): Promise<CleanupReport | null> => {
    if (isCleaningUp) {
      toast.info('Cleanup already in progress');
      return null;
    }

    try {
      setIsCleaningUp(true);
      toast.info('Starting storage cleanup...');

      const report = await storageManager.performCleanup();

      if (report.errors.length > 0) {
        toast.warning(
          `Cleanup completed with ${report.errors.length} warnings. Check console for details.`,
          { duration: 5000 }
        );
        console.warn('Cleanup warnings:', report.errors);
      } else {
        const sizeFreed = formatBytes(report.sizeFreed);
        toast.success(
          `Cleanup successful! Removed ${report.itemsRemoved} items and freed ${sizeFreed}.`,
          { duration: 5000 }
        );
      }

      // Refresh data after cleanup
      await refresh();

      return report;
    } catch (error) {
      console.error('Error during cleanup:', error);
      toast.error('Storage cleanup failed. Please try again.');
      return null;
    } finally {
      setIsCleaningUp(false);
    }
  }, [isCleaningUp, refresh]);

  const getStorageBreakdown = useCallback(async () => {
    try {
      // Get detailed breakdown of storage usage
      const offlineDataSize = getLocalStorageSize('field_staff_offline_data');
      const conflictsSize = getLocalStorageSize('field_staff_sync_conflicts');
      
      // Get photo cache size
      let photosSize = 0;
      try {
        const { photoCacheManager } = await import('@/utils/photoCacheManager');
        const cacheStats = await photoCacheManager.getCacheStats();
        photosSize = cacheStats.totalSize;
      } catch (error) {
        console.warn('Could not get photo cache size:', error);
      }

      // Calculate other storage (auth, settings, etc.)
      const totalLocalStorage = getTotalLocalStorageSize();
      const otherSize = Math.max(0, totalLocalStorage - offlineDataSize - conflictsSize);

      return {
        offlineData: offlineDataSize,
        photos: photosSize,
        conflicts: conflictsSize,
        other: otherSize,
      };
    } catch (error) {
      console.error('Error getting storage breakdown:', error);
      return {
        offlineData: 0,
        photos: 0,
        conflicts: 0,
        other: 0,
      };
    }
  }, []);

  // Helper functions
  const getLocalStorageSize = (key: string): number => {
    try {
      const item = localStorage.getItem(key);
      return item ? new Blob([item]).size : 0;
    } catch (error) {
      return 0;
    }
  };

  const getTotalLocalStorageSize = (): number => {
    let total = 0;
    try {
      for (const key in localStorage) {
        if (localStorage.hasOwnProperty(key)) {
          total += localStorage[key].length + key.length;
        }
      }
    } catch (error) {
      console.error('Error calculating localStorage size:', error);
    }
    return total;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Auto-refresh effect
  useEffect(() => {
    refresh();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(refresh, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refresh]);

  // Clear old alerts periodically
  useEffect(() => {
    const clearOldAlerts = () => {
      setAlertsShown(new Set());
    };

    // Clear alerts every 10 minutes to allow re-showing if storage is still high
    const interval = setInterval(clearOldAlerts, 10 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const actions: StorageMonitorActions = {
    refresh,
    performCleanup,
    getStorageBreakdown,
  };

  return {
    ...state,
    actions,
    isCleaningUp,
    formatBytes,
  };
};

export default useStorageMonitor;
