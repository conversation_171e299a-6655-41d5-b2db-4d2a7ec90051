import { renderHook, act, waitFor } from '@testing-library/react';
import { useAdaptiveGPS } from '../useAdaptiveGPS';

// Mock geolocation
const mockGeolocation = {
  getCurrentPosition: jest.fn(),
  watchPosition: jest.fn(),
  clearWatch: jest.fn(),
};

Object.defineProperty(global.navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true,
});

// Mock battery API
const mockBattery = {
  level: 0.8,
  charging: false,
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
};

Object.defineProperty(global.navigator, 'getBattery', {
  value: jest.fn().mockResolvedValue(mockBattery),
  writable: true,
});

// Mock Page Visibility API
Object.defineProperty(document, 'visibilityState', {
  value: 'visible',
  writable: true,
});

describe('useAdaptiveGPS', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    
    // Reset visibility state
    Object.defineProperty(document, 'visibilityState', {
      value: 'visible',
      writable: true,
    });
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useAdaptiveGPS());

    expect(result.current.isTracking).toBe(false);
    expect(result.current.currentPosition).toBeNull();
    expect(result.current.movementState).toBe('unknown');
    expect(result.current.batteryOptimized).toBe(false);
    expect(result.current.stats.totalPositions).toBe(0);
    expect(result.current.stats.averageAccuracy).toBe(0);
    expect(result.current.stats.batteryOptimizationSavings).toBe(0);
  });

  it('should start tracking with default configuration', async () => {
    const mockPosition = {
      coords: {
        latitude: 40.7128,
        longitude: -74.0060,
        accuracy: 10,
        speed: null,
        heading: null,
      },
      timestamp: Date.now(),
    };

    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success(mockPosition);
    });

    const { result } = renderHook(() => useAdaptiveGPS());

    await act(async () => {
      await result.current.startTracking();
    });

    expect(result.current.isTracking).toBe(true);
    expect(result.current.currentPosition).toEqual({
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 10,
      timestamp: mockPosition.timestamp,
      speed: undefined,
      heading: undefined,
    });
  });

  it('should detect movement and adjust polling interval', async () => {
    const positions = [
      {
        coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10, speed: null, heading: null },
        timestamp: Date.now(),
      },
      {
        coords: { latitude: 40.7130, longitude: -74.0062, accuracy: 10, speed: 5, heading: 90 },
        timestamp: Date.now() + 30000, // 30 seconds later
      },
    ];

    let positionIndex = 0;
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      if (positionIndex < positions.length) {
        success(positions[positionIndex++]);
      }
    });

    const { result } = renderHook(() => useAdaptiveGPS({
      enableBatteryOptimization: true,
      movingInterval: 10000, // 10 seconds when moving
      stationaryInterval: 60000, // 60 seconds when stationary
    }));

    await act(async () => {
      await result.current.startTracking();
    });

    // First position - should be unknown movement state
    expect(result.current.movementState).toBe('unknown');

    // Advance time and trigger second position
    act(() => {
      jest.advanceTimersByTime(30000);
    });

    await waitFor(() => {
      expect(result.current.movementState).toBe('moving');
    });

    expect(result.current.stats.totalPositions).toBe(2);
  });

  it('should detect stationary state and increase polling interval', async () => {
    const stationaryPositions = [
      {
        coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10, speed: null, heading: null },
        timestamp: Date.now(),
      },
      {
        coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 12, speed: 0, heading: null },
        timestamp: Date.now() + 60000, // 1 minute later, same location
      },
      {
        coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 8, speed: 0, heading: null },
        timestamp: Date.now() + 120000, // 2 minutes later, still same location
      },
    ];

    let positionIndex = 0;
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      if (positionIndex < stationaryPositions.length) {
        success(stationaryPositions[positionIndex++]);
      }
    });

    const { result } = renderHook(() => useAdaptiveGPS({
      enableBatteryOptimization: true,
      stationaryThreshold: 50, // 50 meters
      stationaryTimeout: 120000, // 2 minutes
    }));

    await act(async () => {
      await result.current.startTracking();
    });

    // Advance time to trigger multiple position updates
    for (let i = 0; i < 3; i++) {
      act(() => {
        jest.advanceTimersByTime(60000); // 1 minute intervals
      });
      await waitFor(() => {
        expect(result.current.stats.totalPositions).toBeGreaterThan(i);
      });
    }

    expect(result.current.movementState).toBe('stationary');
    expect(result.current.batteryOptimized).toBe(true);
  });

  it('should optimize for background mode', async () => {
    const { result } = renderHook(() => useAdaptiveGPS({
      enableBatteryOptimization: true,
      backgroundInterval: 300000, // 5 minutes in background
    }));

    await act(async () => {
      await result.current.startTracking();
    });

    // Simulate page going to background
    act(() => {
      Object.defineProperty(document, 'visibilityState', {
        value: 'hidden',
        writable: true,
      });
      document.dispatchEvent(new Event('visibilitychange'));
    });

    expect(result.current.batteryOptimized).toBe(true);
    expect(result.current.stats.batteryOptimizationSavings).toBeGreaterThan(0);
  });

  it('should handle low battery optimization', async () => {
    // Mock low battery
    const lowBatteryMock = {
      ...mockBattery,
      level: 0.15, // 15% battery
      charging: false,
    };

    (global.navigator.getBattery as jest.Mock).mockResolvedValue(lowBatteryMock);

    const { result } = renderHook(() => useAdaptiveGPS({
      enableBatteryOptimization: true,
      lowBatteryThreshold: 0.2, // 20%
    }));

    await act(async () => {
      await result.current.startTracking();
    });

    expect(result.current.batteryOptimized).toBe(true);
    expect(result.current.stats.batteryOptimizationSavings).toBeGreaterThan(0);
  });

  it('should calculate distance correctly using Haversine formula', () => {
    const { result } = renderHook(() => useAdaptiveGPS());

    // Test known distance: NYC to Philadelphia (approximately 130 km)
    const distance = result.current.calculateDistance(
      40.7128, -74.0060, // NYC
      39.9526, -75.1652  // Philadelphia
    );

    // Should be approximately 130,000 meters (allow 10% tolerance)
    expect(distance).toBeGreaterThan(117000);
    expect(distance).toBeLessThan(143000);
  });

  it('should handle GPS errors gracefully', async () => {
    const mockError = {
      code: 2, // POSITION_UNAVAILABLE
      message: 'Position unavailable',
      PERMISSION_DENIED: 1,
      POSITION_UNAVAILABLE: 2,
      TIMEOUT: 3,
    };

    mockGeolocation.getCurrentPosition.mockImplementation((_, error) => {
      error(mockError);
    });

    const { result } = renderHook(() => useAdaptiveGPS());

    await act(async () => {
      try {
        await result.current.startTracking();
      } catch (error) {
        // Expected to handle error
      }
    });

    expect(result.current.error).toContain('GPS signal is currently unavailable');
  });

  it('should stop tracking and cleanup resources', async () => {
    const watchId = 123;
    mockGeolocation.watchPosition.mockReturnValue(watchId);

    const { result } = renderHook(() => useAdaptiveGPS());

    await act(async () => {
      await result.current.startTracking();
    });

    expect(result.current.isTracking).toBe(true);

    act(() => {
      result.current.stopTracking();
    });

    expect(result.current.isTracking).toBe(false);
    expect(mockGeolocation.clearWatch).toHaveBeenCalledWith(watchId);
  });

  it('should provide accurate statistics', async () => {
    const positions = [
      { coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10 }, timestamp: Date.now() },
      { coords: { latitude: 40.7130, longitude: -74.0062, accuracy: 15 }, timestamp: Date.now() + 30000 },
      { coords: { latitude: 40.7132, longitude: -74.0064, accuracy: 8 }, timestamp: Date.now() + 60000 },
    ];

    let positionIndex = 0;
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      if (positionIndex < positions.length) {
        success(positions[positionIndex++]);
      }
    });

    const { result } = renderHook(() => useAdaptiveGPS({
      enableBatteryOptimization: true,
    }));

    await act(async () => {
      await result.current.startTracking();
    });

    // Trigger multiple position updates
    for (let i = 0; i < 3; i++) {
      act(() => {
        jest.advanceTimersByTime(30000);
      });
      await waitFor(() => {
        expect(result.current.stats.totalPositions).toBeGreaterThan(i);
      });
    }

    expect(result.current.stats.totalPositions).toBe(3);
    expect(result.current.stats.averageAccuracy).toBe((10 + 15 + 8) / 3);
    expect(result.current.stats.totalDistance).toBeGreaterThan(0);
  });

  it('should handle configuration updates', async () => {
    const { result, rerender } = renderHook(
      (config) => useAdaptiveGPS(config),
      {
        initialProps: {
          enableBatteryOptimization: false,
          movingInterval: 30000,
        },
      }
    );

    await act(async () => {
      await result.current.startTracking();
    });

    expect(result.current.batteryOptimized).toBe(false);

    // Update configuration
    rerender({
      enableBatteryOptimization: true,
      movingInterval: 10000,
    });

    expect(result.current.batteryOptimized).toBe(true);
  });

  it('should reset statistics correctly', async () => {
    const mockPosition = {
      coords: { latitude: 40.7128, longitude: -74.0060, accuracy: 10, speed: null, heading: null },
      timestamp: Date.now(),
    };

    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success(mockPosition);
    });

    const { result } = renderHook(() => useAdaptiveGPS());

    await act(async () => {
      await result.current.startTracking();
    });

    expect(result.current.stats.totalPositions).toBe(1);

    act(() => {
      result.current.resetStats();
    });

    expect(result.current.stats.totalPositions).toBe(0);
    expect(result.current.stats.averageAccuracy).toBe(0);
    expect(result.current.stats.totalDistance).toBe(0);
  });
});
