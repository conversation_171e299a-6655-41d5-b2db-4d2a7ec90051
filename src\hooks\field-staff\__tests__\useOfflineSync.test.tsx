import { renderHook, act } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useOfflineSync } from '../useOfflineSync';
import React from 'react';

// Mock all the utility modules
jest.mock('@/utils/offlineStorage', () => ({
  loadOfflineData: jest.fn(() => []),
  saveOfflineData: jest.fn(),
  loadConflicts: jest.fn(() => []),
  saveConflicts: jest.fn(),
  addToOfflineQueue: jest.fn(() => 'test-id'),
  removeFromOfflineQueue: jest.fn(() => true),
  clearOfflineData: jest.fn(),
  calculateStorageStats: jest.fn(() => Promise.resolve({
    totalItems: 0,
    totalSize: 0,
    conflictItems: 0,
    conflictSize: 0,
    photoItems: 0,
    photoSize: 0,
    lastCleanup: 0,
    storageUsagePercent: 0,
    oldestItem: 0,
    newestItem: 0
  }))
}));

jest.mock('@/utils/syncOperations', () => ({
  syncAllOfflineData: jest.fn(() => Promise.resolve({
    success: true,
    processed: 0,
    failed: 0,
    conflicts: 0,
    errors: []
  })),
  retryFailedItems: jest.fn(),
  checkOnlineStatus: jest.fn(() => true),
  testServerConnectivity: jest.fn(() => Promise.resolve(true))
}));

jest.mock('@/utils/conflictResolution', () => ({
  getAllConflicts: jest.fn(() => []),
  removeConflict: jest.fn(() => true),
  resolveConflict: jest.fn(() => ({}))
}));

jest.mock('@/utils/storageCleanup', () => ({
  performComprehensiveCleanup: jest.fn(() => Promise.resolve({
    itemsRemoved: 0,
    sizeFreed: 0,
    conflictsRemoved: 0,
    photosRemoved: 0
  })),
  isCleanupNeeded: jest.fn(() => Promise.resolve(false)),
  getCleanupRecommendations: jest.fn(() => Promise.resolve({
    shouldCleanup: false,
    recommendations: [],
    potentialSavings: 0
  }))
}));

jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
    warning: jest.fn(),
    info: jest.fn()
  }
}));

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useOfflineSync (Refactored)', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    expect(result.current.syncStatus).toEqual(
      expect.objectContaining({
        isOnline: true,
        pendingItems: 0,
        lastSyncTime: null,
        isSyncing: false,
        syncProgress: 0,
        failedItems: 0,
        conflictItems: 0,
      })
    );
    expect(result.current.conflicts).toEqual([]);
  });

  it('should add items to offline queue', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      const id = result.current.addToOfflineQueue('check_in', {
        school_id: 'test-school',
        latitude: 40.7128,
        longitude: -74.0060,
      });
      expect(id).toBe('test-id');
    });
  });

  it('should sync offline data successfully', async () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const syncResult = await result.current.syncOfflineData();
      expect(syncResult.success).toBe(true);
    });
  });

  it('should resolve conflicts', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      const resolved = result.current.resolveConflict('conflict-1', 'CLIENT_WINS');
      expect(resolved).toBe(true);
    });
  });

  it('should get sync statistics', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    const stats = result.current.getSyncStats();
    expect(stats).toEqual(
      expect.objectContaining({
        totalPending: 0,
        highPriority: 0,
        failed: 0,
        conflicts: 0,
        oldestItem: null,
      })
    );
  });

  it('should get storage statistics', async () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const stats = await result.current.getStorageStats();
      expect(stats).toEqual(
        expect.objectContaining({
          totalItems: 0,
          totalSize: 0,
          conflictItems: 0,
          conflictSize: 0,
        })
      );
    });
  });

  it('should perform cleanup', async () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const cleanupResult = await result.current.performComprehensiveCleanup();
      expect(cleanupResult).toEqual(
        expect.objectContaining({
          itemsRemoved: 0,
          sizeFreed: 0,
          conflictsRemoved: 0,
          photosRemoved: 0,
        })
      );
    });
  });

  it('should clear offline data', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.clearOfflineData();
    });

    // Should call the clearOfflineData utility
    const { clearOfflineData } = require('@/utils/offlineStorage');
    expect(clearOfflineData).toHaveBeenCalled();
  });

  it('should remove items from offline queue', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      const removed = result.current.removeFromOfflineQueue('test-id');
      expect(removed).toBe(true);
    });
  });
});
