import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { 
  MapPin, 
  Navigation, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Wifi,
  WifiOff,
  Smartphone,
  Target,
  Timer
} from 'lucide-react';
import { 
  useGPSPosition, 
  useGPSCheckIn, 
  useGPSCheckOut, 
  useCurrentCheckInStatus,
  calculateDistance 
} from '@/hooks/attendance/useGPSTracking';
import { useSchools } from '@/hooks/useSchools';
import { useAttendanceSessions } from '@/hooks/attendance/useAttendanceSessions';

interface GPSCheckInProps {
  defaultSchoolId?: string;
  defaultSessionId?: string;
}

const GPSCheckIn: React.FC<GPSCheckInProps> = ({
  defaultSchoolId,
  defaultSessionId,
}) => {
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || '');
  const [selectedSessionId, setSelectedSessionId] = useState(defaultSessionId || '');
  const [notes, setNotes] = useState('');
  const [addressDescription, setAddressDescription] = useState('');

  const { data: schools } = useSchools();
  const { data: sessions } = useAttendanceSessions(selectedSchoolId);
  const { data: currentCheckIn } = useCurrentCheckInStatus();
  
  const {
    position,
    error: gpsError,
    isLoading: gpsLoading,
    getCurrentPosition,
    startWatching,
    stopWatching,
    isWatching,
  } = useGPSPosition();

  const checkIn = useGPSCheckIn();
  const checkOut = useGPSCheckOut();

  // Auto-start GPS watching when component mounts
  useEffect(() => {
    startWatching();
    return () => stopWatching();
  }, [startWatching, stopWatching]);

  // Get reverse geocoding for address (simplified)
  useEffect(() => {
    if (position && !addressDescription) {
      setAddressDescription(`${position.latitude.toFixed(6)}, ${position.longitude.toFixed(6)}`);
    }
  }, [position, addressDescription]);

  const selectedSchool = schools?.find(s => s.id === selectedSchoolId);
  const isOnline = navigator.onLine;
  
  // Calculate distance from school if both position and school location are available
  const distanceFromSchool = position && selectedSchool?.location_coordinates 
    ? calculateDistance(
        position.latitude,
        position.longitude,
        selectedSchool.location_coordinates[1], // latitude
        selectedSchool.location_coordinates[0]  // longitude
      )
    : null;

  const isNearSchool = distanceFromSchool !== null && distanceFromSchool <= 100; // Within 100 meters

  const handleCheckIn = async () => {
    if (!position || !selectedSchoolId) return;

    try {
      await getCurrentPosition(); // Get fresh position
      
      await checkIn.mutateAsync({
        school_id: selectedSchoolId,
        session_id: selectedSessionId && selectedSessionId !== 'none' ? selectedSessionId : undefined,
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        address_description: addressDescription,
        verification_method: 'gps',
        device_info: { notes },
        network_info: { online: isOnline },
      });

      setNotes('');
    } catch (error) {
      console.error('Check-in failed:', error);
    }
  };

  const handleCheckOut = async () => {
    try {
      // Pass the current check-in ID if available, otherwise let the hook find it
      await checkOut.mutateAsync(currentCheckIn?.id);
    } catch (error) {
      console.error('Check-out failed:', error);
    }
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy <= 10) return 'text-green-600';
    if (accuracy <= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyLabel = (accuracy: number) => {
    if (accuracy <= 10) return 'Excellent';
    if (accuracy <= 50) return 'Good';
    return 'Poor';
  };

  return (
    <div className="space-y-6">
      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            GPS Check-In Status
          </CardTitle>
          <CardDescription>
            Track your location and check in/out of school visits
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {/* GPS Status */}
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className={`p-2 rounded-lg ${position ? 'bg-green-100' : 'bg-red-100'}`}>
                <Navigation className={`h-5 w-5 ${position ? 'text-green-600' : 'text-red-600'}`} />
              </div>
              <div>
                <div className="font-medium">
                  {position ? 'GPS Active' : 'GPS Unavailable'}
                </div>
                <div className="text-sm text-gray-600">
                  {position 
                    ? `±${position.accuracy.toFixed(0)}m accuracy`
                    : gpsError || 'Waiting for location...'
                  }
                </div>
              </div>
            </div>

            {/* Network Status */}
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className={`p-2 rounded-lg ${isOnline ? 'bg-green-100' : 'bg-red-100'}`}>
                {isOnline ? (
                  <Wifi className="h-5 w-5 text-green-600" />
                ) : (
                  <WifiOff className="h-5 w-5 text-red-600" />
                )}
              </div>
              <div>
                <div className="font-medium">
                  {isOnline ? 'Online' : 'Offline'}
                </div>
                <div className="text-sm text-gray-600">
                  {isOnline ? 'Real-time sync' : 'Will sync when online'}
                </div>
              </div>
            </div>

            {/* Check-in Status */}
            <div className="flex items-center gap-3 p-3 border rounded-lg">
              <div className={`p-2 rounded-lg ${currentCheckIn ? 'bg-blue-100' : 'bg-gray-100'}`}>
                {currentCheckIn ? (
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                ) : (
                  <XCircle className="h-5 w-5 text-gray-600" />
                )}
              </div>
              <div>
                <div className="font-medium">
                  {currentCheckIn ? 'Checked In' : 'Not Checked In'}
                </div>
                <div className="text-sm text-gray-600">
                  {currentCheckIn 
                    ? `Since ${new Date(currentCheckIn.check_in_time).toLocaleTimeString()}`
                    : 'Ready to check in'
                  }
                </div>
              </div>
            </div>
          </div>

          {/* GPS Details */}
          {position && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">Current Location</div>
                <div className="text-sm text-gray-600">
                  Lat: {position.latitude.toFixed(6)}<br />
                  Lng: {position.longitude.toFixed(6)}
                </div>
              </div>
              <div className="space-y-2">
                <div className="text-sm font-medium">Accuracy</div>
                <div className="flex items-center gap-2">
                  <Badge className={getAccuracyColor(position.accuracy)}>
                    {getAccuracyLabel(position.accuracy)}
                  </Badge>
                  <span className="text-sm text-gray-600">
                    ±{position.accuracy.toFixed(0)} meters
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* Distance from School */}
          {distanceFromSchool !== null && selectedSchool && (
            <Alert className={isNearSchool ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}>
              <Target className={`h-4 w-4 ${isNearSchool ? 'text-green-600' : 'text-yellow-600'}`} />
              <AlertDescription>
                You are <strong>{distanceFromSchool.toFixed(0)} meters</strong> from {selectedSchool.name}
                {isNearSchool && ' - Close enough for check-in!'}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Check-in Form */}
      {!currentCheckIn ? (
        <Card>
          <CardHeader>
            <CardTitle>Check In</CardTitle>
            <CardDescription>
              Select your school and session to check in
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">School *</label>
                <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select school" />
                  </SelectTrigger>
                  <SelectContent>
                    {schools?.map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Session (Optional)</label>
                <Select value={selectedSessionId} onValueChange={setSelectedSessionId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select session" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">No specific session</SelectItem>
                    {sessions?.map((session) => (
                      <SelectItem key={session.id} value={session.id}>
                        {session.session_name} - {new Date(session.session_date).toLocaleDateString()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Location Description</label>
              <Textarea
                value={addressDescription}
                onChange={(e) => setAddressDescription(e.target.value)}
                placeholder="Describe your current location..."
                rows={2}
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-2">Notes</label>
              <Textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Any additional notes about this visit..."
                rows={2}
              />
            </div>

            <Button
              onClick={handleCheckIn}
              disabled={!position || !selectedSchoolId || checkIn.isPending || gpsLoading}
              className="w-full"
              size="lg"
            >
              {checkIn.isPending ? (
                'Checking In...'
              ) : gpsLoading ? (
                'Getting Location...'
              ) : (
                <>
                  <MapPin className="h-4 w-4 mr-2" />
                  Check In with GPS
                </>
              )}
            </Button>

            {gpsError && (
              <Alert variant="destructive">
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {gpsError}. Please enable location services and try again.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      ) : (
        /* Check-out Card */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Timer className="h-5 w-5" />
              Currently Checked In
            </CardTitle>
            <CardDescription>
              You are checked in at {schools?.find(s => s.id === currentCheckIn.school_id)?.name}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium">Check-in Time</div>
                  <div className="text-sm text-gray-600">
                    {new Date(currentCheckIn.check_in_time).toLocaleString()}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium">Duration</div>
                  <div className="text-sm text-gray-600">
                    {Math.floor((Date.now() - new Date(currentCheckIn.check_in_time).getTime()) / (1000 * 60))} minutes
                  </div>
                </div>
              </div>

              {currentCheckIn.distance_from_school && (
                <div>
                  <div className="text-sm font-medium">Distance from School</div>
                  <div className="text-sm text-gray-600">
                    {currentCheckIn.distance_from_school.toFixed(0)} meters
                  </div>
                </div>
              )}

              <Button
                onClick={handleCheckOut}
                disabled={checkOut.isPending}
                variant="outline"
                className="w-full"
                size="lg"
              >
                {checkOut.isPending ? 'Checking Out...' : 'Check Out'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default GPSCheckIn;
