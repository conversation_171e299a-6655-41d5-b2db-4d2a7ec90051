import { useState, useEffect, useCallback, useRef } from 'react';
import { 
  photoUploadQueue, 
  PhotoUploadQueueItem, 
  PhotoUploadProgress, 
  PhotoUploadQueueStats 
} from '@/utils/photoUploadQueue';
import { toast } from 'sonner';

export interface UsePhotoUploadQueueReturn {
  // Queue state
  queueStats: PhotoUploadQueueStats;
  queueItems: PhotoUploadQueueItem[];
  isProcessing: boolean;
  
  // Actions
  addToQueue: (
    photoId: string,
    fileName: string,
    blob: Blob,
    options?: {
      fieldReportId?: string;
      distributionId?: string;
      priority?: PhotoUploadQueueItem['priority'];
      maxRetries?: number;
      metadata?: PhotoUploadQueueItem['metadata'];
    }
  ) => string;
  removeFromQueue: (queueId: string) => boolean;
  retryFailedUploads: () => void;
  clearCompleted: () => void;
  pauseQueue: () => void;
  resumeQueue: () => void;
  
  // Progress tracking
  subscribeToProgress: (queueId: string, callback: (progress: PhotoUploadProgress) => void) => void;
  unsubscribeFromProgress: (queueId: string) => void;
}

export const usePhotoUploadQueue = (): UsePhotoUploadQueueReturn => {
  const [queueStats, setQueueStats] = useState<PhotoUploadQueueStats>(() => 
    photoUploadQueue.getQueueStats()
  );
  const [queueItems, setQueueItems] = useState<PhotoUploadQueueItem[]>(() => 
    photoUploadQueue.getAllItems()
  );
  const [isProcessing, setIsProcessing] = useState(true);
  
  const updateIntervalRef = useRef<NodeJS.Timeout>();
  const progressCallbacksRef = useRef<Map<string, (progress: PhotoUploadProgress) => void>>(new Map());

  // Update queue state periodically
  const updateQueueState = useCallback(() => {
    setQueueStats(photoUploadQueue.getQueueStats());
    setQueueItems(photoUploadQueue.getAllItems());
  }, []);

  // Set up periodic updates
  useEffect(() => {
    updateQueueState();
    
    updateIntervalRef.current = setInterval(updateQueueState, 1000);
    
    return () => {
      if (updateIntervalRef.current) {
        clearInterval(updateIntervalRef.current);
      }
    };
  }, [updateQueueState]);

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => {
      setIsProcessing(true);
      toast.success('Connection restored. Resuming photo uploads...');
    };

    const handleOffline = () => {
      setIsProcessing(false);
      toast.warning('Connection lost. Photo uploads paused.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Set initial processing state
    setIsProcessing(navigator.onLine);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Add photo to queue
  const addToQueue = useCallback((
    photoId: string,
    fileName: string,
    blob: Blob,
    options: {
      fieldReportId?: string;
      distributionId?: string;
      priority?: PhotoUploadQueueItem['priority'];
      maxRetries?: number;
      metadata?: PhotoUploadQueueItem['metadata'];
    } = {}
  ): string => {
    const queueId = photoUploadQueue.addToQueue(photoId, fileName, blob, options);
    updateQueueState();
    return queueId;
  }, [updateQueueState]);

  // Remove from queue
  const removeFromQueue = useCallback((queueId: string): boolean => {
    const success = photoUploadQueue.removeFromQueue(queueId);
    if (success) {
      updateQueueState();
      // Clean up any progress callbacks
      progressCallbacksRef.current.delete(queueId);
    }
    return success;
  }, [updateQueueState]);

  // Retry failed uploads
  const retryFailedUploads = useCallback(() => {
    photoUploadQueue.retryFailedUploads();
    updateQueueState();
  }, [updateQueueState]);

  // Clear completed uploads
  const clearCompleted = useCallback(() => {
    photoUploadQueue.clearCompleted();
    updateQueueState();
  }, [updateQueueState]);

  // Pause queue
  const pauseQueue = useCallback(() => {
    photoUploadQueue.pauseProcessing();
    setIsProcessing(false);
    toast.info('Photo upload queue paused');
  }, []);

  // Resume queue
  const resumeQueue = useCallback(() => {
    photoUploadQueue.resumeProcessing();
    setIsProcessing(true);
  }, []);

  // Subscribe to progress updates
  const subscribeToProgress = useCallback((
    queueId: string, 
    callback: (progress: PhotoUploadProgress) => void
  ) => {
    progressCallbacksRef.current.set(queueId, callback);
    photoUploadQueue.subscribeToProgress(queueId, callback);
  }, []);

  // Unsubscribe from progress updates
  const unsubscribeFromProgress = useCallback((queueId: string) => {
    progressCallbacksRef.current.delete(queueId);
    photoUploadQueue.unsubscribeFromProgress(queueId);
  }, []);

  // Clean up progress callbacks on unmount
  useEffect(() => {
    return () => {
      progressCallbacksRef.current.forEach((_, queueId) => {
        photoUploadQueue.unsubscribeFromProgress(queueId);
      });
      progressCallbacksRef.current.clear();
    };
  }, []);

  return {
    queueStats,
    queueItems,
    isProcessing,
    addToQueue,
    removeFromQueue,
    retryFailedUploads,
    clearCompleted,
    pauseQueue,
    resumeQueue,
    subscribeToProgress,
    unsubscribeFromProgress,
  };
};

// Hook for monitoring a specific photo upload
export const usePhotoUploadProgress = (queueId: string) => {
  const [progress, setProgress] = useState<PhotoUploadProgress | null>(null);
  const { subscribeToProgress, unsubscribeFromProgress } = usePhotoUploadQueue();

  useEffect(() => {
    if (!queueId) return;

    const handleProgress = (progressData: PhotoUploadProgress) => {
      setProgress(progressData);
    };

    subscribeToProgress(queueId, handleProgress);

    return () => {
      unsubscribeFromProgress(queueId);
    };
  }, [queueId, subscribeToProgress, unsubscribeFromProgress]);

  return progress;
};

// Hook for queue statistics only (lighter weight)
export const usePhotoUploadStats = () => {
  const [stats, setStats] = useState<PhotoUploadQueueStats>(() => 
    photoUploadQueue.getQueueStats()
  );

  useEffect(() => {
    const updateStats = () => {
      setStats(photoUploadQueue.getQueueStats());
    };

    const interval = setInterval(updateStats, 2000); // Update every 2 seconds

    return () => clearInterval(interval);
  }, []);

  return stats;
};
