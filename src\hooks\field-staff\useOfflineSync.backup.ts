import { useState, useEffect, useCallback } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { FieldO<PERSON>ation<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, exponentialBackoff } from '@/utils/errorHandling';

interface OfflineData {
  id: string;
  type: 'check_in' | 'check_out' | 'field_report' | 'photo_upload';
  data: Record<string, unknown>;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  checksum?: string;
  version?: number;
  conflictResolution?: 'CLIENT_WINS' | 'SERVER_WINS' | 'MERGE' | 'MANUAL';
  // Photo-specific fields
  blobData?: Blob;
  uploadProgress?: number;
  uploadSessionId?: string;
}

interface OfflineSyncStatus {
  isOnline: boolean;
  pendingItems: number;
  lastSyncTime: Date | null;
  isSyncing: boolean;
  syncProgress: number;
  failedItems: number;
  conflictItems: number;
  storageUsagePercent: number;
  lastCleanup: Date | null;
  totalStorageSize: number;
}

interface SyncConflict {
  id: string;
  localData: Record<string, unknown>;
  serverData: Record<string, unknown>;
  conflictFields: string[];
  timestamp: number;
}

interface StorageStats {
  totalItems: number;
  totalSize: number;
  conflictItems: number;
  conflictSize: number;
  photoItems: number;
  photoSize: number;
  lastCleanup: number;
  storageUsagePercent: number;
  oldestItem: number;
  newestItem: number;
}

interface CleanupResult {
  itemsRemoved: number;
  sizeFreed: number;
  conflictsRemoved: number;
  photosRemoved: number;
}

const OFFLINE_STORAGE_KEY = 'field_staff_offline_data';
const CONFLICTS_STORAGE_KEY = 'field_staff_sync_conflicts';
const PHOTO_BLOB_STORE = 'field_staff_photo_blobs';
const STORAGE_STATS_KEY = 'field_staff_storage_stats';
const MAX_RETRY_ATTEMPTS = 5;
const SYNC_RETRY_DELAY = 2000; // 2 seconds base delay
const MAX_OFFLINE_ITEMS = 300; // Further reduced to prevent memory issues
const MAX_STORAGE_SIZE = 8 * 1024 * 1024; // 8MB max for localStorage data
const MAX_CONFLICT_ITEMS = 25; // Reduced conflict storage
const CLEANUP_THRESHOLD = 0.75; // Clean up when 75% full (more aggressive)
const SUCCESSFUL_ITEM_RETENTION = 12 * 60 * 60 * 1000; // Keep successful items for 12 hours
const FAILED_ITEM_RETENTION = 3 * 24 * 60 * 60 * 1000; // Keep failed items for 3 days
const CRITICAL_STORAGE_THRESHOLD = 0.95; // Emergency cleanup at 95%
const SYNC_BATCH_SIZE = 10; // Process items in smaller batches
const CONFLICT_RETENTION = 30 * 24 * 60 * 60 * 1000; // Keep conflicts for 30 days

// IndexedDB utilities for photo blob storage
const openPhotoDatabase = (): Promise<IDBDatabase> => {
  return new Promise((resolve, reject) => {
    const request = indexedDB.open(PHOTO_BLOB_STORE, 1);

    request.onerror = () => reject(request.error);
    request.onsuccess = () => resolve(request.result);

    request.onupgradeneeded = (event) => {
      const db = (event.target as IDBOpenDBRequest).result;
      if (!db.objectStoreNames.contains('photos')) {
        db.createObjectStore('photos', { keyPath: 'id' });
      }
    };
  });
};

const storePhotoBlob = async (id: string, blob: Blob): Promise<void> => {
  try {
    const db = await openPhotoDatabase();
    const transaction = db.transaction(['photos'], 'readwrite');
    const store = transaction.objectStore('photos');

    await new Promise<void>((resolve, reject) => {
      const request = store.put({ id, blob, timestamp: Date.now() });
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Failed to store photo blob:', error);
  }
};

const getPhotoBlob = async (id: string): Promise<Blob | null> => {
  try {
    const db = await openPhotoDatabase();
    const transaction = db.transaction(['photos'], 'readonly');
    const store = transaction.objectStore('photos');

    return new Promise<Blob | null>((resolve, reject) => {
      const request = store.get(id);
      request.onsuccess = () => {
        const result = request.result;
        resolve(result ? result.blob : null);
      };
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Failed to get photo blob:', error);
    return null;
  }
};

const deletePhotoBlob = async (id: string): Promise<void> => {
  try {
    const db = await openPhotoDatabase();
    const transaction = db.transaction(['photos'], 'readwrite');
    const store = transaction.objectStore('photos');

    await new Promise<void>((resolve, reject) => {
      const request = store.delete(id);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  } catch (error) {
    console.error('Failed to delete photo blob:', error);
  }
};

export const useOfflineSync = () => {
  const [syncStatus, setSyncStatus] = useState<OfflineSyncStatus>({
    isOnline: navigator.onLine,
    pendingItems: 0,
    lastSyncTime: null,
    isSyncing: false,
    syncProgress: 0,
    failedItems: 0,
    conflictItems: 0,
  });

  const [conflicts, setConflicts] = useState<SyncConflict[]>([]);
  const queryClient = useQueryClient();

  // Generate checksum for data integrity
  const generateChecksum = useCallback((data: Record<string, unknown>): string => {
    const str = JSON.stringify(data, Object.keys(data).sort());
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }, []);

  // Load offline data from localStorage with integrity check
  const loadOfflineData = useCallback((): OfflineData[] => {
    try {
      const stored = localStorage.getItem(OFFLINE_STORAGE_KEY);
      if (!stored) return [];

      const data = JSON.parse(stored);

      // Validate data integrity
      return data.filter((item: OfflineData) => {
        if (!item.checksum) return true; // Legacy data without checksum
        const currentChecksum = generateChecksum(item.data);
        if (currentChecksum !== item.checksum) {
          console.warn(`Data integrity check failed for item ${item.id}`);
          return false;
        }
        return true;
      });
    } catch (error) {
      console.error('Failed to load offline data:', error);
      // Clear corrupted data
      localStorage.removeItem(OFFLINE_STORAGE_KEY);
      return [];
    }
  }, [generateChecksum]);

  // Load conflicts from localStorage
  const loadConflicts = useCallback((): ConflictData[] => {
    try {
      const stored = localStorage.getItem(CONFLICTS_STORAGE_KEY);
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to load conflicts:', error);
      localStorage.removeItem(CONFLICTS_STORAGE_KEY);
      return [];
    }
  }, []);

  // Calculate storage statistics
  const getStorageStats = useCallback(async (): Promise<StorageStats> => {
    try {
      const offlineData = loadOfflineData();
      const conflicts = loadConflicts();

      // Calculate localStorage sizes
      const offlineDataSize = new Blob([JSON.stringify(offlineData)]).size;
      const conflictSize = new Blob([JSON.stringify(conflicts)]).size;

      // Get photo cache stats
      let photoStats = { totalItems: 0, totalSize: 0 };
      try {
        const { photoCacheManager } = await import('@/utils/photoCacheManager');
        const cacheStats = await photoCacheManager.getCacheStats();
        photoStats = {
          totalItems: cacheStats.totalItems,
          totalSize: cacheStats.totalSize
        };
      } catch (error) {
        console.warn('Could not get photo cache stats:', error);
      }

      const totalSize = offlineDataSize + conflictSize + photoStats.totalSize;
      const storageUsagePercent = (totalSize / MAX_STORAGE_SIZE) * 100;

      const timestamps = offlineData.map(item => item.timestamp).filter(Boolean);
      const oldestItem = timestamps.length > 0 ? Math.min(...timestamps) : 0;
      const newestItem = timestamps.length > 0 ? Math.max(...timestamps) : 0;

      return {
        totalItems: offlineData.length,
        totalSize: offlineDataSize,
        conflictItems: conflicts.length,
        conflictSize,
        photoItems: photoStats.totalItems,
        photoSize: photoStats.totalSize,
        lastCleanup: parseInt(localStorage.getItem(`${STORAGE_STATS_KEY}_last_cleanup`) || '0'),
        storageUsagePercent,
        oldestItem,
        newestItem
      };
    } catch (error) {
      console.error('Error calculating storage stats:', error);
      return {
        totalItems: 0,
        totalSize: 0,
        conflictItems: 0,
        conflictSize: 0,
        photoItems: 0,
        photoSize: 0,
        lastCleanup: 0,
        storageUsagePercent: 0,
        oldestItem: 0,
        newestItem: 0
      };
    }
  }, [loadOfflineData, loadConflicts]);

  // Enhanced cleanup function for comprehensive storage management
  const performComprehensiveCleanup = useCallback(async (forceCleanup = false): Promise<CleanupResult> => {
    const now = Date.now();
    const stats = await getStorageStats();

    // Check if cleanup is needed
    if (!forceCleanup && stats.storageUsagePercent < (CLEANUP_THRESHOLD * 100)) {
      return { itemsRemoved: 0, sizeFreed: 0, conflictsRemoved: 0, photosRemoved: 0 };
    }

    let itemsRemoved = 0;
    let sizeFreed = 0;
    let conflictsRemoved = 0;
    let photosRemoved = 0;

    try {
      // 1. Clean up old successful items
      const offlineData = loadOfflineData();
      const itemsToKeep = offlineData.filter(item => {
        const age = now - item.timestamp;

        // Keep items that are still pending or uploading
        if (item.retryCount < item.maxRetries) return true;

        // Keep failed items for longer (they might need manual intervention)
        if (item.retryCount >= item.maxRetries && age < FAILED_ITEM_RETENTION) return true;

        // Remove old successful items (these would have been synced)
        if (item.retryCount === 0 && age > SUCCESSFUL_ITEM_RETENTION) {
          itemsRemoved++;
          return false;
        }

        return true;
      });

      // 2. If still over limit, remove oldest items by priority
      if (itemsToKeep.length > MAX_OFFLINE_ITEMS) {
        const sortedItems = itemsToKeep.sort((a, b) => {
          const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };

          // Keep higher priority items
          if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
            return priorityOrder[b.priority] - priorityOrder[a.priority];
          }

          // Among same priority, keep newer items
          return b.timestamp - a.timestamp;
        });

        const finalItems = sortedItems.slice(0, MAX_OFFLINE_ITEMS);
        itemsRemoved += itemsToKeep.length - finalItems.length;
        itemsToKeep.splice(0, itemsToKeep.length, ...finalItems);
      }

      // Calculate size freed
      const originalSize = new Blob([JSON.stringify(offlineData)]).size;
      const newSize = new Blob([JSON.stringify(itemsToKeep)]).size;
      sizeFreed += originalSize - newSize;

      // Save cleaned data
      localStorage.setItem(OFFLINE_STORAGE_KEY, JSON.stringify(itemsToKeep));

      // 3. Clean up old conflicts
      const conflicts = loadConflicts();
      const conflictsToKeep = conflicts.filter(conflict => {
        const age = now - conflict.timestamp;
        if (age > CONFLICT_RETENTION) {
          conflictsRemoved++;
          return false;
        }
        return true;
      });

      // Limit conflict storage
      if (conflictsToKeep.length > MAX_CONFLICT_ITEMS) {
        const sortedConflicts = conflictsToKeep.sort((a, b) => b.timestamp - a.timestamp);
        const finalConflicts = sortedConflicts.slice(0, MAX_CONFLICT_ITEMS);
        conflictsRemoved += conflictsToKeep.length - finalConflicts.length;
        conflictsToKeep.splice(0, conflictsToKeep.length, ...finalConflicts);
      }

      saveConflicts(conflictsToKeep);

      // 4. Clean up photo cache
      try {
        const { photoCacheManager } = await import('@/utils/photoCacheManager');
        const beforePhotoStats = await photoCacheManager.getCacheStats();
        await photoCacheManager.performCleanup();
        const afterPhotoStats = await photoCacheManager.getCacheStats();
        photosRemoved = beforePhotoStats.totalItems - afterPhotoStats.totalItems;
      } catch (error) {
        console.warn('Could not clean photo cache:', error);
      }

      // Update cleanup timestamp
      localStorage.setItem(`${STORAGE_STATS_KEY}_last_cleanup`, now.toString());

      // Update sync status
      setSyncStatus(prev => ({
        ...prev,
        pendingItems: itemsToKeep.length,
        failedItems: itemsToKeep.filter(item => item.retryCount >= item.maxRetries).length,
        conflictItems: conflictsToKeep.length
      }));

      console.log(`Cleanup completed: ${itemsRemoved} items, ${conflictsRemoved} conflicts, ${photosRemoved} photos removed`);

      return { itemsRemoved, sizeFreed, conflictsRemoved, photosRemoved };
    } catch (error) {
      console.error('Error during comprehensive cleanup:', error);
      return { itemsRemoved, sizeFreed, conflictsRemoved, photosRemoved };
    }
  }, [getStorageStats, loadOfflineData, loadConflicts, saveConflicts]);

  // Save offline data to localStorage with enhanced storage management
  const saveOfflineData = useCallback(async (data: OfflineData[]) => {
    try {
      // Check if cleanup is needed before saving
      const stats = await getStorageStats();
      if (stats.storageUsagePercent > (CLEANUP_THRESHOLD * 100)) {
        await performComprehensiveCleanup();
      }

      // Sort by priority and timestamp
      const sortedData = data.sort((a, b) => {
        const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
        if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        }
        return a.timestamp - b.timestamp;
      });

      // Note: Photo uploads are now handled by dedicated photo upload queue
      // Filter out photo uploads from general sync queue
      const otherData = sortedData.filter(item => item.type !== 'photo_upload');

      // Limit storage size
      const limitedData = otherData.slice(-MAX_OFFLINE_ITEMS);

      localStorage.setItem(OFFLINE_STORAGE_KEY, JSON.stringify(limitedData));
      setSyncStatus(prev => ({
        ...prev,
        pendingItems: limitedData.length,
        failedItems: limitedData.filter(item => item.retryCount >= item.maxRetries).length
      }));
    } catch (error) {
      console.error('Failed to save offline data:', error);

      // Try to free up space by performing emergency cleanup
      if (error.name === 'QuotaExceededError') {
        try {
          await performComprehensiveCleanup(true);
          // Try saving again with reduced data
          const reducedData = data.slice(-Math.floor(MAX_OFFLINE_ITEMS / 2));
          localStorage.setItem(OFFLINE_STORAGE_KEY, JSON.stringify(reducedData));
          toast.warning('Storage space low. Performed emergency cleanup.');
        } catch (secondError) {
          toast.error('Failed to save data offline - storage full');
        }
      } else {
        toast.error('Failed to save data offline');
      }
    }
  }, [getStorageStats, performComprehensiveCleanup]);

  const saveConflicts = useCallback((conflicts: SyncConflict[]) => {
    try {
      localStorage.setItem(CONFLICTS_STORAGE_KEY, JSON.stringify(conflicts));
      setSyncStatus(prev => ({ ...prev, conflictItems: conflicts.length }));
    } catch (error) {
      console.error('Failed to save conflicts:', error);
    }
  }, []);

  // Add data to offline queue with priority and integrity check
  const addToOfflineQueue = useCallback((
    type: OfflineData['type'],
    data: Record<string, unknown>,
    priority: OfflineData['priority'] = 'MEDIUM',
    maxRetries: number = MAX_RETRY_ATTEMPTS
  ) => {
    const checksum = generateChecksum(data);
    const offlineItem: OfflineData = {
      id: `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      data,
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries,
      priority,
      checksum,
      version: 1,
      conflictResolution: 'CLIENT_WINS',
    };

    const existingData = loadOfflineData();

    // Sort by priority and timestamp
    const updatedData = [...existingData, offlineItem].sort((a, b) => {
      const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
    });

    saveOfflineData(updatedData);

    toast.info('Data saved offline. Will sync when connection is restored.');
    return offlineItem.id;
  }, [loadOfflineData, saveOfflineData, generateChecksum]);

  // Remove item from offline queue
  const removeFromOfflineQueue = useCallback((id: string) => {
    const existingData = loadOfflineData();
    const updatedData = existingData.filter(item => item.id !== id);
    saveOfflineData(updatedData);
  }, [loadOfflineData, saveOfflineData]);

  // Detect and handle sync conflicts
  const detectConflict = useCallback(async (item: OfflineData): Promise<SyncConflict | null> => {
    try {
      // Check if server data has been modified since offline item was created
      let serverData;

      switch (item.type) {
        case 'check_in': {
          const { data: attendanceData } = await supabase
            .from('field_staff_attendance')
            .select('*')
            .eq('staff_id', item.data.staff_id)
            .eq('school_id', item.data.school_id)
            .eq('attendance_date', new Date(item.timestamp).toISOString().split('T')[0])
            .maybeSingle();
          serverData = attendanceData;
          break;
        }

        case 'field_report': {
          const { data: reportData } = await supabase
            .from('field_reports')
            .select('*')
            .eq('id', item.data.id)
            .maybeSingle();
          serverData = reportData;
          break;
        }

        default:
          return null;
      }

      if (!serverData) return null;

      // Compare timestamps and data
      const serverModified = new Date(serverData.updated_at || serverData.created_at).getTime();
      const localModified = item.timestamp;

      if (serverModified > localModified) {
        // Find conflicting fields
        const conflictFields: string[] = [];
        Object.keys(item.data).forEach(key => {
          if (serverData[key] !== item.data[key]) {
            conflictFields.push(key);
          }
        });

        if (conflictFields.length > 0) {
          return {
            id: item.id,
            localData: item.data,
            serverData,
            conflictFields,
            timestamp: Date.now(),
          };
        }
      }

      return null;
    } catch (error) {
      console.error('Error detecting conflict:', error);
      return null;
    }
  }, []);

  // Sync a single offline item with conflict detection
  const syncOfflineItem = useCallback(async (item: OfflineData): Promise<{ success: boolean; conflict?: SyncConflict }> => {
    try {
      // Check for conflicts first
      const conflict = await detectConflict(item);
      if (conflict) {
        return { success: false, conflict };
      }

      let result;

      switch (item.type) {
        case 'check_in':
          result = await supabase.rpc('field_staff_checkin', {
            p_school_id: item.data.school_id,
            p_latitude: item.data.latitude,
            p_longitude: item.data.longitude,
            p_accuracy: item.data.accuracy,
            p_address: item.data.address,
            p_verification_method: item.data.verification_method || 'gps',
            p_device_info: item.data.device_info || {},
            p_network_info: item.data.network_info || {},
            p_offline_sync: true,
          });
          break;

        case 'check_out':
          result = await supabase.rpc('field_staff_checkout', {
            p_attendance_id: item.data.attendance_id,
            p_activity_type: item.data.activity_type,
            p_latitude: item.data.latitude,
            p_longitude: item.data.longitude,
            p_accuracy: item.data.accuracy,
            p_address: item.data.address,
            p_notes: item.data.notes,
            p_round_table_sessions: item.data.round_table_sessions || 0,
            p_total_students: item.data.total_students || 0,
            p_students_per_session: item.data.students_per_session || 8,
            p_activities_conducted: item.data.activities_conducted || [],
            p_topics_covered: item.data.topics_covered || [],
            p_challenges: item.data.challenges,
            p_wins: item.data.wins,
            p_observations: item.data.observations,
            p_lessons_learned: item.data.lessons_learned,
            p_follow_up_required: item.data.follow_up_required || false,
            p_follow_up_actions: item.data.follow_up_actions,
            p_photos: item.data.photos || [],
            p_offline_sync: true,
          });
          break;

        case 'field_report':
          // Handle standalone field report updates with version check
          result = await supabase
            .from('field_reports')
            .update({ ...item.data, version: (item.version || 1) + 1 })
            .eq('id', item.data.id)
            .eq('version', item.version || 1); // Optimistic locking
          break;

        case 'photo_upload':
          // Photo uploads are now handled by dedicated photo upload queue
          // Skip processing here and mark as successful to remove from general queue
          result = { data: { message: 'Photo upload handled by dedicated queue' }, error: null };
          break;

        default:
          throw new Error(`Unknown offline item type: ${item.type}`);
      }

      if (result.error) {
        // Handle specific error types
        if (result.error.code === '23505') { // Unique constraint violation
          throw new FieldOperationError(
            'SYNC_CONFLICT',
            'Data conflict detected during sync',
            'SYNC',
            'MEDIUM'
          );
        }
        throw result.error;
      }

      return { success: true };
    } catch (error) {
      console.error(`Failed to sync ${item.type}:`, error);

      // Handle network errors
      if (!navigator.onLine) {
        throw FieldOperationError.fromNetworkError(error as Error, false);
      }

      return { success: false };
    }
  }, [detectConflict]);

  // Enhanced sync with batch processing and improved reliability
  const syncOfflineData = useCallback(async () => {
    if (!navigator.onLine) {
      return;
    }

    const offlineData = loadOfflineData();
    if (offlineData.length === 0) {
      setSyncStatus(prev => ({ ...prev, lastSyncTime: Date.now() }));
      return;
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, syncProgress: 0 }));

    const itemsToRemove: string[] = [];
    const itemsToRetry: OfflineData[] = [];
    const newConflicts: SyncConflict[] = [];
    const totalItems = offlineData.length;
    let processedItems = 0;
    let successfulSyncs = 0;

    // Process items in batches for better performance and reliability
    for (let batchStart = 0; batchStart < offlineData.length; batchStart += SYNC_BATCH_SIZE) {
      const batch = offlineData.slice(batchStart, batchStart + SYNC_BATCH_SIZE);

      // Process batch items concurrently but with limited concurrency
      const batchPromises = batch.map(async (item) => {
        try {
          // Update progress
          processedItems++;
          setSyncStatus(prev => ({
            ...prev,
            syncProgress: Math.round((processedItems / totalItems) * 100)
          }));

          // Add exponential backoff for retries
          if (item.retryCount > 0) {
            const delay = exponentialBackoff(item.retryCount, SYNC_RETRY_DELAY);
            await new Promise(resolve => setTimeout(resolve, delay));
          }

          const result = await syncOfflineItem(item);

          if (result.success) {
            itemsToRemove.push(item.id);
            toast.success(`Synced ${item.type} from ${new Date(item.timestamp).toLocaleString()}`);
          } else if (result.conflict) {
            // Handle conflict based on resolution strategy
            switch (item.conflictResolution) {
              case 'CLIENT_WINS':
                // Force sync with client data
                // Implementation would depend on specific requirements
                break;
              case 'SERVER_WINS':
                // Discard client changes
                itemsToRemove.push(item.id);
                toast.warning(`Server version used for ${item.type} - local changes discarded`);
                break;
              case 'MANUAL':
                // Store conflict for manual resolution
                newConflicts.push(result.conflict);
                itemsToRemove.push(item.id);
                break;
              default:
                // Default to manual resolution
                newConflicts.push(result.conflict);
                itemsToRemove.push(item.id);
            }
          } else {
            // Increment retry count
            const updatedItem = {
              ...item,
              retryCount: item.retryCount + 1,
            };

            if (updatedItem.retryCount < updatedItem.maxRetries) {
              itemsToRetry.push(updatedItem);
            } else {
              // Max retries reached, remove from queue
              itemsToRemove.push(item.id);
              toast.error(`Failed to sync ${item.type} after ${item.maxRetries} attempts`);
            }
          }
        } catch (error) {
          console.error(`Sync error for item ${item.id}:`, error);

          // Handle specific errors
          if (error instanceof FieldOperationError) {
            await ErrorHandler.handle(error);
          }

          // Increment retry count
          const updatedItem = {
            ...item,
            retryCount: item.retryCount + 1,
          };

          if (updatedItem.retryCount < updatedItem.maxRetries) {
            itemsToRetry.push(updatedItem);
          } else {
            itemsToRemove.push(item.id);
            toast.error(`Failed to sync ${item.type} after ${item.maxRetries} attempts`);
          }
        }
      });

      // Wait for all batch items to complete
      await Promise.all(batchPromises);
    }

    // Update offline data
    const remainingData = offlineData
      .filter(item => !itemsToRemove.includes(item.id))
      .map(item => {
        const retryItem = itemsToRetry.find(retry => retry.id === item.id);
        return retryItem || item;
      });

    saveOfflineData(remainingData);

    // Save new conflicts
    if (newConflicts.length > 0) {
      const existingConflicts = loadConflicts();
      saveConflicts([...existingConflicts, ...newConflicts]);
      toast.warning(`${newConflicts.length} sync conflicts require manual resolution`);
    }

    // Invalidate queries to refresh data
    queryClient.invalidateQueries({ queryKey: ['field-staff-attendance'] });
    queryClient.invalidateQueries({ queryKey: ['field-staff-status'] });
    queryClient.invalidateQueries({ queryKey: ['field-staff-timesheets'] });

    setSyncStatus(prev => ({
      ...prev,
      isSyncing: false,
      lastSyncTime: new Date(),
      syncProgress: 100,
    }));

    if (itemsToRemove.length > 0) {
      toast.success(`Successfully synced ${itemsToRemove.length} offline items`);
    }
  }, [loadOfflineData, saveOfflineData, syncOfflineItem, queryClient, loadConflicts, saveConflicts]);

  // Handle online/offline status changes
  useEffect(() => {
    const handleOnline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: true }));
      toast.success('Connection restored. Syncing offline data...');
      syncOfflineData();
    };

    const handleOffline = () => {
      setSyncStatus(prev => ({ ...prev, isOnline: false }));
      toast.warning('Connection lost. Data will be saved offline.');
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [syncOfflineData]);

  // Initialize sync status with storage information
  useEffect(() => {
    const initializeStatus = async () => {
      const offlineData = loadOfflineData();
      const stats = await getStorageStats();

      setSyncStatus(prev => ({
        ...prev,
        pendingItems: offlineData.length,
        isOnline: navigator.onLine,
        storageUsagePercent: stats.storageUsagePercent,
        lastCleanup: stats.lastCleanup ? new Date(stats.lastCleanup) : null,
        totalStorageSize: stats.totalSize + stats.conflictSize + stats.photoSize,
        conflictItems: stats.conflictItems
      }));

      // Auto-sync on mount if online
      if (navigator.onLine && offlineData.length > 0) {
        syncOfflineData();
      }
    };

    initializeStatus();
  }, [loadOfflineData, syncOfflineData, getStorageStats]);

  // Periodic sync attempt
  useEffect(() => {
    const interval = setInterval(() => {
      if (navigator.onLine && syncStatus.pendingItems > 0 && !syncStatus.isSyncing) {
        syncOfflineData();
      }
    }, SYNC_RETRY_DELAY);

    return () => clearInterval(interval);
  }, [syncOfflineData, syncStatus.pendingItems, syncStatus.isSyncing]);

  // Periodic cleanup task
  useEffect(() => {
    const cleanupInterval = setInterval(async () => {
      try {
        const stats = await getStorageStats();
        const now = Date.now();
        const timeSinceLastCleanup = now - stats.lastCleanup;

        // Perform cleanup if:
        // 1. Storage usage is high (>80%)
        // 2. It's been more than 6 hours since last cleanup
        // 3. There are more than 100 items in storage
        if (
          stats.storageUsagePercent > 80 ||
          timeSinceLastCleanup > (6 * 60 * 60 * 1000) ||
          stats.totalItems > 100
        ) {
          console.log('Performing scheduled cleanup...');
          const result = await performComprehensiveCleanup();

          if (result.itemsRemoved > 0 || result.conflictsRemoved > 0 || result.photosRemoved > 0) {
            console.log(`Scheduled cleanup completed:`, result);

            // Update storage stats in sync status
            const updatedStats = await getStorageStats();
            setSyncStatus(prev => ({
              ...prev,
              storageUsagePercent: updatedStats.storageUsagePercent,
              lastCleanup: new Date(updatedStats.lastCleanup),
              totalStorageSize: updatedStats.totalSize + updatedStats.conflictSize + updatedStats.photoSize,
              conflictItems: updatedStats.conflictItems
            }));
          }
        }
      } catch (error) {
        console.error('Error during scheduled cleanup:', error);
      }
    }, 30 * 60 * 1000); // Run every 30 minutes

    return () => clearInterval(cleanupInterval);
  }, [getStorageStats, performComprehensiveCleanup]);

  // Resolve sync conflict
  const resolveConflict = useCallback((conflictId: string, resolution: 'CLIENT' | 'SERVER' | 'MERGE', mergedData?: Record<string, unknown>) => {
    const existingConflicts = loadConflicts();
    const conflict = existingConflicts.find(c => c.id === conflictId);

    if (!conflict) return;

    let resolvedData;
    switch (resolution) {
      case 'CLIENT':
        resolvedData = conflict.localData;
        break;
      case 'SERVER':
        resolvedData = conflict.serverData;
        break;
      case 'MERGE':
        resolvedData = mergedData || { ...conflict.serverData, ...conflict.localData };
        break;
    }

    // Remove conflict from storage
    const updatedConflicts = existingConflicts.filter(c => c.id !== conflictId);
    saveConflicts(updatedConflicts);

    // Add resolved data back to sync queue if needed
    if (resolution === 'CLIENT' || resolution === 'MERGE') {
      // Determine type from conflict data
      const type = conflict.localData.attendance_id ? 'check_out' :
                   conflict.localData.school_id ? 'check_in' : 'field_report';

      addToOfflineQueue(type as OfflineData['type'], resolvedData, 'HIGH');
    }

    toast.success('Conflict resolved successfully');
  }, [loadConflicts, saveConflicts, addToOfflineQueue]);

  // Clear all offline data (for testing/debugging)
  const clearOfflineData = useCallback(() => {
    localStorage.removeItem(OFFLINE_STORAGE_KEY);
    localStorage.removeItem(CONFLICTS_STORAGE_KEY);
    setSyncStatus(prev => ({
      ...prev,
      pendingItems: 0,
      conflictItems: 0,
      failedItems: 0
    }));
    setConflicts([]);
    toast.success('Offline data cleared');
  }, []);

  // Get sync statistics
  const getSyncStats = useCallback(() => {
    const offlineData = loadOfflineData();
    const conflicts = loadConflicts();

    return {
      totalPending: offlineData.length,
      highPriority: offlineData.filter(item => item.priority === 'HIGH' || item.priority === 'CRITICAL').length,
      failed: offlineData.filter(item => item.retryCount >= item.maxRetries).length,
      conflicts: conflicts.length,
      oldestItem: offlineData.length > 0 ? Math.min(...offlineData.map(item => item.timestamp)) : null,
    };
  }, [loadOfflineData, loadConflicts]);

  // Initialize conflicts on mount
  useEffect(() => {
    const existingConflicts = loadConflicts();
    setConflicts(existingConflicts);
  }, [loadConflicts]);

  return {
    syncStatus,
    conflicts,
    addToOfflineQueue,
    removeFromOfflineQueue,
    syncOfflineData,
    clearOfflineData,
    loadOfflineData,
    resolveConflict,
    getSyncStats,
    getStorageStats,
    performComprehensiveCleanup,
  };
};
